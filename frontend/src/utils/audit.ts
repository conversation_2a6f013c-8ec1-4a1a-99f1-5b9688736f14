/**
 * 🔐 NIS2-COMPLIANT: Audit Logging System
 * Comprehensive audit logging for security compliance and monitoring.
 * Tracks all user actions, data access, and system events for regulatory compliance.
 */

import { configManager } from './config';

export interface AuditEvent {
  id: string;
  timestamp: string;
  userId?: string;
  sessionId: string;
  eventType: AuditEventType;
  category: AuditCategory;
  action: string;
  resource?: string;
  resourceId?: string;
  details: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  success: boolean;
  errorMessage?: string;
}

export enum AuditEventType {
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  DATA_ACCESS = 'data_access',
  DATA_MODIFICATION = 'data_modification',
  SYSTEM_ACCESS = 'system_access',
  CONFIGURATION_CHANGE = 'configuration_change',
  SECURITY_EVENT = 'security_event',
  ANALYTICS_ACCESS = 'analytics_access',
  EXPORT_DATA = 'export_data',
  USER_MANAGEMENT = 'user_management',
}

export enum AuditCategory {
  SECURITY = 'security',
  DATA_PROTECTION = 'data_protection',
  SYSTEM_ADMINISTRATION = 'system_administration',
  USER_ACTIVITY = 'user_activity',
  COMPLIANCE = 'compliance',
  PERFORMANCE = 'performance',
}

class AuditLogger {
  private static instance: AuditLogger;
  private eventQueue: AuditEvent[] = [];
  private sessionId: string;
  private userId?: string;
  private isOnline: boolean = navigator.onLine;

  private constructor() {
    this.sessionId = this.generateSessionId();
    this.setupNetworkHandlers();
    this.startPeriodicFlush();
  }

  public static getInstance(): AuditLogger {
    if (!AuditLogger.instance) {
      AuditLogger.instance = new AuditLogger();
    }
    return AuditLogger.instance;
  }

  private generateSessionId(): string {
    return `audit-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private setupNetworkHandlers(): void {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.flushEvents();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
    });
  }

  private startPeriodicFlush(): void {
    // Flush events every 30 seconds
    setInterval(() => {
      if (this.isOnline && this.eventQueue.length > 0) {
        this.flushEvents();
      }
    }, 30000);
  }

  public setUserId(userId: string): void {
    this.userId = userId;
    this.logEvent({
      eventType: AuditEventType.AUTHENTICATION,
      category: AuditCategory.SECURITY,
      action: 'user_session_established',
      details: { userId },
      severity: 'medium',
      success: true,
    });
  }

  public logEvent(event: Partial<AuditEvent>): void {
    if (!configManager.isComplianceEnabled('audit')) {
      return; // Audit logging disabled
    }

    const auditEvent: AuditEvent = {
      id: this.generateEventId(),
      timestamp: new Date().toISOString(),
      userId: this.userId,
      sessionId: this.sessionId,
      eventType: event.eventType || AuditEventType.SYSTEM_ACCESS,
      category: event.category || AuditCategory.USER_ACTIVITY,
      action: event.action || 'unknown_action',
      resource: event.resource,
      resourceId: event.resourceId,
      details: event.details || {},
      ipAddress: this.getClientIP(),
      userAgent: navigator.userAgent,
      severity: event.severity || 'low',
      success: event.success !== undefined ? event.success : true,
      errorMessage: event.errorMessage,
    };

    this.eventQueue.push(auditEvent);

    // Log to console in development
    if (configManager.isDevelopment()) {
      console.log('🔐 [AUDIT]', {
        type: auditEvent.eventType,
        action: auditEvent.action,
        severity: auditEvent.severity,
        success: auditEvent.success,
        details: auditEvent.details,
      });
    }

    // Immediate flush for critical events
    if (auditEvent.severity === 'critical' && this.isOnline) {
      this.flushEvents();
    }
  }

  private generateEventId(): string {
    return `evt-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private getClientIP(): string {
    // In a real implementation, this would be handled by the backend
    return 'client-ip-placeholder';
  }

  private async flushEvents(): Promise<void> {
    if (this.eventQueue.length === 0) return;

    const eventsToFlush = [...this.eventQueue];
    this.eventQueue = [];

    try {
      // Send to backend audit service
      const response = await fetch('/api/audit/events', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getAuthToken()}`,
        },
        body: JSON.stringify({ events: eventsToFlush }),
      });

      if (!response.ok) {
        throw new Error(`Audit flush failed: ${response.status}`);
      }

      if (configManager.isDevelopment()) {
        console.log(`🔐 [AUDIT] Flushed ${eventsToFlush.length} events to backend`);
      }
    } catch (error) {
      console.error('🔐 [AUDIT] Failed to flush events:', error);
      
      // Re-queue events for retry
      this.eventQueue.unshift(...eventsToFlush);
      
      // Store in localStorage as backup
      this.storeEventsLocally(eventsToFlush);
    }
  }

  private storeEventsLocally(events: AuditEvent[]): void {
    try {
      const existingEvents = JSON.parse(localStorage.getItem('ehrx_audit_backup') || '[]');
      const allEvents = [...existingEvents, ...events];
      
      // Keep only last 1000 events to prevent storage overflow
      const recentEvents = allEvents.slice(-1000);
      
      localStorage.setItem('ehrx_audit_backup', JSON.stringify(recentEvents));
    } catch (error) {
      console.error('🔐 [AUDIT] Failed to store events locally:', error);
    }
  }

  private getAuthToken(): string {
    return sessionStorage.getItem('ehrx_auth_token') || '';
  }

  // Convenience methods for common audit events
  public logAnalyticsAccess(widgetType: string, filters?: any): void {
    this.logEvent({
      eventType: AuditEventType.ANALYTICS_ACCESS,
      category: AuditCategory.DATA_PROTECTION,
      action: 'analytics_widget_accessed',
      resource: 'analytics_widget',
      resourceId: widgetType,
      details: { widgetType, filters },
      severity: 'low',
      success: true,
    });
  }

  public logDataExport(exportType: string, recordCount: number): void {
    this.logEvent({
      eventType: AuditEventType.EXPORT_DATA,
      category: AuditCategory.DATA_PROTECTION,
      action: 'data_exported',
      resource: 'export',
      details: { exportType, recordCount },
      severity: 'medium',
      success: true,
    });
  }

  public logSecurityEvent(action: string, details: any, severity: 'low' | 'medium' | 'high' | 'critical' = 'high'): void {
    this.logEvent({
      eventType: AuditEventType.SECURITY_EVENT,
      category: AuditCategory.SECURITY,
      action,
      details,
      severity,
      success: false,
    });
  }

  public logConfigurationChange(setting: string, oldValue: any, newValue: any): void {
    this.logEvent({
      eventType: AuditEventType.CONFIGURATION_CHANGE,
      category: AuditCategory.SYSTEM_ADMINISTRATION,
      action: 'configuration_updated',
      resource: 'system_configuration',
      resourceId: setting,
      details: { setting, oldValue, newValue },
      severity: 'medium',
      success: true,
    });
  }

  public logUserAction(action: string, resource?: string, details?: any): void {
    this.logEvent({
      eventType: AuditEventType.USER_MANAGEMENT,
      category: AuditCategory.USER_ACTIVITY,
      action,
      resource,
      details: details || {},
      severity: 'low',
      success: true,
    });
  }

  public logError(error: Error, context: string): void {
    this.logEvent({
      eventType: AuditEventType.SYSTEM_ACCESS,
      category: AuditCategory.PERFORMANCE,
      action: 'system_error',
      details: {
        error: error.message,
        stack: error.stack,
        context,
      },
      severity: 'high',
      success: false,
      errorMessage: error.message,
    });
  }
}

// Export singleton instance
export const auditLogger = AuditLogger.getInstance();

// Export convenience functions
export const logAnalyticsAccess = (widgetType: string, filters?: any) => 
  auditLogger.logAnalyticsAccess(widgetType, filters);

export const logDataExport = (exportType: string, recordCount: number) => 
  auditLogger.logDataExport(exportType, recordCount);

export const logSecurityEvent = (action: string, details: any, severity?: 'low' | 'medium' | 'high' | 'critical') => 
  auditLogger.logSecurityEvent(action, details, severity);

export const logConfigurationChange = (setting: string, oldValue: any, newValue: any) => 
  auditLogger.logConfigurationChange(setting, oldValue, newValue);

export const logUserAction = (action: string, resource?: string, details?: any) => 
  auditLogger.logUserAction(action, resource, details);

export const logError = (error: Error, context: string) => 
  auditLogger.logError(error, context);
