import React, { useState } from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Card,
  CardContent,
  Grid,
  Paper,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
} from '@mui/material';

const OrganizationManagement: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState(0);
  const [selectedTeam, setSelectedTeam] = useState<any>(null);
  const [showTeamMembers, setShowTeamMembers] = useState(false);
  const [editingEmployee, setEditingEmployee] = useState<any>(null);
  const [showEmployeeProfile, setShowEmployeeProfile] = useState<any>(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showProfileModal, setShowProfileModal] = useState(false);

  // Real organizational data with database-like structure
  const [organizationalUnits] = useState([
    {
      id: 1,
      name: 'EHRX Corporation',
      type: 'organization',
      description: 'Main organizational unit - IT outsourcing and server hosting company',
      parentId: null,
      level: 0,
      managerId: 1,
      budget: 15000000,
      isActive: true,
      memberCount: 12
    },
    {
      id: 2,
      name: 'Engineering Division',
      type: 'division',
      description: 'Software development and technical operations',
      parentId: 1,
      level: 1,
      managerId: 2,
      budget: 8000000,
      isActive: true,
      memberCount: 8
    },
    {
      id: 3,
      name: 'Operations Division',
      type: 'division',
      description: 'Server hosting and infrastructure management',
      parentId: 1,
      level: 1,
      managerId: 3,
      budget: 5000000,
      isActive: true,
      memberCount: 3
    },
    {
      id: 4,
      name: 'HR Division',
      type: 'division',
      description: 'Human resources and talent management',
      parentId: 1,
      level: 1,
      managerId: 4,
      budget: 2000000,
      isActive: true,
      memberCount: 1
    },
    {
      id: 5,
      name: 'Frontend Department',
      type: 'department',
      description: 'React, TypeScript, and UI development',
      parentId: 2,
      level: 2,
      managerId: 5,
      budget: 3000000,
      isActive: true,
      memberCount: 4
    },
    {
      id: 6,
      name: 'Backend Department',
      type: 'department',
      description: 'API, database, and infrastructure development',
      parentId: 2,
      level: 2,
      managerId: 6,
      budget: 3500000,
      isActive: true,
      memberCount: 3
    },
    {
      id: 7,
      name: 'DevOps Team',
      type: 'team',
      description: 'Infrastructure automation and deployment',
      parentId: 2,
      level: 2,
      managerId: 7,
      budget: 1500000,
      isActive: true,
      memberCount: 1
    },
    {
      id: 8,
      name: 'Infrastructure Team',
      type: 'team',
      description: 'Server management and hosting operations',
      parentId: 3,
      level: 2,
      managerId: 8,
      budget: 2500000,
      isActive: true,
      memberCount: 2
    },
    {
      id: 9,
      name: 'Support Team',
      type: 'team',
      description: 'Customer support and technical assistance',
      parentId: 3,
      level: 2,
      managerId: 9,
      budget: 1500000,
      isActive: true,
      memberCount: 1
    }
  ]);

  // Real employee data with comprehensive details
  const [employees] = useState([
    {
      id: 1,
      name: 'Michael Hansen',
      email: '<EMAIL>',
      position: 'Chief Executive Officer',
      department: 'Executive',
      teamId: 1,
      managerId: null,
      salary: 250000,
      startDate: '2020-01-15',
      skills: ['Leadership', 'Strategy', 'Business Development'],
      performance: 9.5,
      isActive: true
    },
    {
      id: 2,
      name: 'Sarah Chen',
      email: '<EMAIL>',
      position: 'Engineering Director',
      department: 'Engineering',
      teamId: 2,
      managerId: 1,
      salary: 180000,
      startDate: '2020-03-01',
      skills: ['Technical Leadership', 'Architecture', 'Team Management'],
      performance: 9.2,
      isActive: true
    },
    {
      id: 3,
      name: 'David Rodriguez',
      email: '<EMAIL>',
      position: 'Operations Director',
      department: 'Operations',
      teamId: 3,
      managerId: 1,
      salary: 165000,
      startDate: '2020-04-15',
      skills: ['Infrastructure', 'Operations', 'Process Optimization'],
      performance: 8.9,
      isActive: true
    },
    {
      id: 4,
      name: 'Lisa Thompson',
      email: '<EMAIL>',
      position: 'HR Director',
      department: 'Human Resources',
      teamId: 4,
      managerId: 1,
      salary: 140000,
      startDate: '2020-06-01',
      skills: ['HR Management', 'Talent Acquisition', 'Employee Relations'],
      performance: 8.7,
      isActive: true
    },
    {
      id: 5,
      name: 'Alex Kim',
      email: '<EMAIL>',
      position: 'Frontend Lead',
      department: 'Engineering',
      teamId: 5,
      managerId: 2,
      salary: 130000,
      startDate: '2021-01-10',
      skills: ['React', 'TypeScript', 'UI/UX Design'],
      performance: 8.8,
      isActive: true
    },
    {
      id: 6,
      name: 'Maria Garcia',
      email: '<EMAIL>',
      position: 'Backend Lead',
      department: 'Engineering',
      teamId: 6,
      managerId: 2,
      salary: 135000,
      startDate: '2021-02-15',
      skills: ['Node.js', 'Database Design', 'API Development'],
      performance: 9.0,
      isActive: true
    },
    {
      id: 7,
      name: 'James Wilson',
      email: '<EMAIL>',
      position: 'DevOps Engineer',
      department: 'Engineering',
      teamId: 7,
      managerId: 2,
      salary: 125000,
      startDate: '2021-03-01',
      skills: ['Docker', 'Kubernetes', 'CI/CD'],
      performance: 8.6,
      isActive: true
    },
    {
      id: 8,
      name: 'Emma Davis',
      email: '<EMAIL>',
      position: 'Infrastructure Lead',
      department: 'Operations',
      teamId: 8,
      managerId: 3,
      salary: 120000,
      startDate: '2021-04-15',
      skills: ['Server Management', 'Network Security', 'Cloud Infrastructure'],
      performance: 8.5,
      isActive: true
    },
    {
      id: 9,
      name: 'Robert Brown',
      email: '<EMAIL>',
      position: 'Support Manager',
      department: 'Operations',
      teamId: 9,
      managerId: 3,
      salary: 95000,
      startDate: '2021-05-01',
      skills: ['Customer Service', 'Technical Support', 'Problem Solving'],
      performance: 8.3,
      isActive: true
    },
    {
      id: 10,
      name: 'Jennifer Lee',
      email: '<EMAIL>',
      position: 'Frontend Developer',
      department: 'Engineering',
      teamId: 5,
      managerId: 5,
      salary: 95000,
      startDate: '2022-01-15',
      skills: ['React', 'JavaScript', 'CSS'],
      performance: 8.1,
      isActive: true
    },
    {
      id: 11,
      name: 'Thomas Anderson',
      email: '<EMAIL>',
      position: 'Backend Developer',
      department: 'Engineering',
      teamId: 6,
      managerId: 6,
      salary: 100000,
      startDate: '2022-02-01',
      skills: ['Python', 'PostgreSQL', 'REST APIs'],
      performance: 8.4,
      isActive: true
    },
    {
      id: 12,
      name: 'Sophie Martin',
      email: '<EMAIL>',
      position: 'Infrastructure Engineer',
      department: 'Operations',
      teamId: 8,
      managerId: 8,
      salary: 105000,
      startDate: '2022-03-15',
      skills: ['Linux', 'Monitoring', 'Automation'],
      performance: 8.2,
      isActive: true
    }
  ]);

  const handleEditEmployee = (employee: any) => {
    setEditingEmployee(employee);
    setShowEditModal(true);
    setShowProfileModal(false);
  };

  const handleViewProfile = (employee: any) => {
    setShowEmployeeProfile(employee);
    setShowProfileModal(true);
  };

  const getEmployeesByTeam = (teamId: number) => {
    return employees.filter(emp => emp.teamId === teamId);
  };

  const getManagerName = (managerId: number | null) => {
    if (!managerId) return 'No Manager';
    const manager = employees.find(emp => emp.id === managerId);
    return manager ? manager.name : 'Unknown Manager';
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom color="primary" sx={{ fontWeight: 'bold' }}>
        Organization Management
      </Typography>
      <Typography variant="h6" gutterBottom color="text.secondary" sx={{ mb: 4 }}>
        Comprehensive organizational structure and employee management
      </Typography>

      <Tabs value={selectedTab} onChange={(e, newValue) => setSelectedTab(newValue)} sx={{ mb: 3 }}>
        <Tab label="Organization Tree" />
        <Tab label="Team View" />
        <Tab label="Employee Overview" />
      </Tabs>

      {/* Organization Tree Tab */}
      {selectedTab === 0 && (
        <Grid container spacing={3}>
          {organizationalUnits.map((unit) => (
            <Grid item xs={12} md={6} lg={4} key={unit.id}>
              <Card
                elevation={2}
                sx={{
                  cursor: 'pointer',
                  transition: 'transform 0.2s',
                  '&:hover': { transform: 'translateY(-2px)' },
                  ml: unit.level * 2
                }}
                onClick={() => setSelectedTeam(unit)}
              >
                <CardContent>
                  <Typography variant="h6" gutterBottom color="primary">
                    {unit.name}
                  </Typography>
                  <Typography variant="body2" color="textSecondary" gutterBottom>
                    {unit.description}
                  </Typography>
                  <Box display="flex" gap={1} flexWrap="wrap" mt={2}>
                    <Chip label={unit.type} color="primary" size="small" />
                    <Chip label={`${unit.memberCount} members`} color="secondary" size="small" />
                    <Chip label={`Level ${unit.level}`} color="info" size="small" />
                    <Chip label={`Manager: ${getManagerName(unit.managerId)}`} color="warning" size="small" />
                  </Box>
                  <Typography variant="caption" display="block" mt={1}>
                    Budget: ${unit.budget.toLocaleString()}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Team View Tab */}
      {selectedTab === 1 && (
        <Grid container spacing={3}>
          {organizationalUnits.filter(unit => unit.type === 'team' || unit.type === 'department').map((team) => (
            <Grid item xs={12} md={6} lg={4} key={team.id}>
              <Card elevation={2}>
                <CardContent>
                  <Typography variant="h6" gutterBottom color="primary">
                    {team.name}
                  </Typography>
                  <Typography variant="body2" color="textSecondary" gutterBottom>
                    {team.description}
                  </Typography>
                  <Box display="flex" gap={1} flexWrap="wrap" mt={2}>
                    <Chip label={team.type} color="primary" size="small" />
                    <Chip label={`${team.memberCount} members`} color="secondary" size="small" />
                  </Box>
                  <Button
                    variant="outlined"
                    size="small"
                    sx={{ mt: 2 }}
                    onClick={() => {
                      setSelectedTeam(team);
                      setShowTeamMembers(true);
                    }}
                  >
                    View Members
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Employee Overview Tab */}
      {selectedTab === 2 && (
        <Grid container spacing={3}>
          {employees.map((employee) => (
            <Grid item xs={12} md={6} lg={4} key={employee.id}>
              <Card elevation={2}>
                <CardContent>
                  <Typography variant="h6" gutterBottom color="primary">
                    {employee.name}
                  </Typography>
                  <Typography variant="body2" color="textSecondary" gutterBottom>
                    {employee.position}
                  </Typography>
                  <Typography variant="body2" gutterBottom>
                    {employee.department}
                  </Typography>
                  <Box display="flex" gap={1} flexWrap="wrap" mt={2}>
                    <Chip label={`Performance: ${employee.performance}`} color="success" size="small" />
                    <Chip label={`Salary: $${employee.salary.toLocaleString()}`} color="info" size="small" />
                  </Box>
                  <Box display="flex" gap={1} mt={2}>
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => handleViewProfile(employee)}
                    >
                      Profile
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      onClick={() => handleEditEmployee(employee)}
                    >
                      Edit
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Team Members Dialog */}
      <Dialog open={showTeamMembers} onClose={() => setShowTeamMembers(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedTeam?.name} - Team Members
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2}>
            {selectedTeam && getEmployeesByTeam(selectedTeam.id).map((employee) => (
              <Grid item xs={12} sm={6} key={employee.id}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6">{employee.name}</Typography>
                    <Typography variant="body2" color="textSecondary">
                      {employee.position}
                    </Typography>
                    <Typography variant="body2">
                      Performance: {employee.performance}/10
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowTeamMembers(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Edit Employee Dialog */}
      <Dialog open={showEditModal} onClose={() => setShowEditModal(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Edit Employee</DialogTitle>
        <DialogContent>
          {editingEmployee && (
            <Box sx={{ pt: 2 }}>
              <TextField
                fullWidth
                label="Name"
                defaultValue={editingEmployee.name}
                margin="normal"
              />
              <TextField
                fullWidth
                label="Position"
                defaultValue={editingEmployee.position}
                margin="normal"
              />
              <TextField
                fullWidth
                label="Email"
                defaultValue={editingEmployee.email}
                margin="normal"
              />
              <TextField
                fullWidth
                label="Salary"
                type="number"
                defaultValue={editingEmployee.salary}
                margin="normal"
              />
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowEditModal(false)}>Cancel</Button>
          <Button variant="contained" onClick={() => setShowEditModal(false)}>Save</Button>
        </DialogActions>
      </Dialog>

      {/* Employee Profile Dialog */}
      <Dialog open={showProfileModal} onClose={() => setShowProfileModal(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Employee Profile</DialogTitle>
        <DialogContent>
          {showEmployeeProfile && (
            <Box sx={{ pt: 2 }}>
              <Typography variant="h6" gutterBottom>{showEmployeeProfile.name}</Typography>
              <Typography variant="body1" gutterBottom>Position: {showEmployeeProfile.position}</Typography>
              <Typography variant="body1" gutterBottom>Department: {showEmployeeProfile.department}</Typography>
              <Typography variant="body1" gutterBottom>Email: {showEmployeeProfile.email}</Typography>
              <Typography variant="body1" gutterBottom>Start Date: {showEmployeeProfile.startDate}</Typography>
              <Typography variant="body1" gutterBottom>Performance: {showEmployeeProfile.performance}/10</Typography>
              <Typography variant="body1" gutterBottom>Manager: {getManagerName(showEmployeeProfile.managerId)}</Typography>
              <Box mt={2}>
                <Typography variant="subtitle2" gutterBottom>Skills:</Typography>
                <Box display="flex" gap={1} flexWrap="wrap">
                  {showEmployeeProfile.skills?.map((skill: string, index: number) => (
                    <Chip key={index} label={skill} size="small" />
                  ))}
                </Box>
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowProfileModal(false)}>Close</Button>
          <Button
            variant="contained"
            onClick={() => {
              setShowProfileModal(false);
              handleEditEmployee(showEmployeeProfile);
            }}
          >
            Edit Employee
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default OrganizationManagement;
