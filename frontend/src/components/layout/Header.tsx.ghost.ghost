import React, { useState } from 'react';
import { User } from '../../types';

interface HeaderProps {
  user: User | null;
  onLogout: () => void;
}

const Header: React.FC<HeaderProps> = ({ user, onLogout }) => {
  const [dropdownOpen, setDropdownOpen] = useState(false);

  return (
    <header className="bg-white shadow-md py-4 px-6 flex justify-between items-center">
      <div className="flex items-center">
        <h2 className="text-xl font-semibold text-gray-800">
          {window.location.pathname === '/dashboard' ? 'Dashboard' : 
           window.location.pathname === '/assessments' ? 'Assessments' :
           window.location.pathname === '/teams' ? 'Team Management' :
           window.location.pathname === '/templates' ? 'Assessment Templates' :
           window.location.pathname === '/users' ? 'User Management' :
           window.location.pathname === '/reports' ? 'Reports' :
           window.location.pathname === '/profile' ? 'My Profile' : 'EHRX'}
        </h2>
      </div>
      
      <div className="relative">
        {user && (
          <>
            <button 
              onClick={() => setDropdownOpen(!dropdownOpen)}
              className="flex items-center space-x-2 focus:outline-none"
              aria-label="User menu"
              aria-haspopup="true"
              aria-expanded={dropdownOpen}
            >
              <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white">
                {user.firstName.charAt(0)}{user.lastName.charAt(0)}
              </div>
              <span className="text-gray-700">{user.firstName} {user.lastName}</span>
              <span className="material-icons text-gray-500">
                {dropdownOpen ? 'arrow_drop_up' : 'arrow_drop_down'}
              </span>
            </button>
            
            {dropdownOpen && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10">
                <a href="/profile" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  Profile
                </a>
                <a href="/settings" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  Settings
                </a>
                <button
                  onClick={onLogout}
                  className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  Logout
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </header>
  );
};

export default Header;
