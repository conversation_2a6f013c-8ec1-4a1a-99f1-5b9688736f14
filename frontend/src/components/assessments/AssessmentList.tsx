import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { PerformanceAssessment, AssessmentStatus, UserRole } from '../../types';
import { ApiService } from '../../services/api';
import {
  getAssessmentStatusColor,
  getAssessmentFilterColor,
  formatAssessmentStatus,
  isAssessmentOverdue
} from '../../utils/assessment-utils';

interface AssessmentListProps {
  userRole: UserRole;
  userId?: number;
  teamId?: number;
}

const AssessmentList: React.FC<AssessmentListProps> = ({ userRole, userId, teamId }) => {
  const [assessments, setAssessments] = useState<PerformanceAssessment[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState<AssessmentStatus | 'all'>('all');
  const [searchTerm, setSearchTerm] = useState<string>('');

  useEffect(() => {
    const fetchAssessments = async () => {
      try {
        setIsLoading(true);

        // 🔐 NIS2-COMPLIANT: Use centralized API service for real data
        const response = await ApiService.getAssessments({
          status: statusFilter !== 'all' ? statusFilter : undefined,
          employeeId: userId,
          teamId: teamId,
          page: 1,
          limit: 50
        });

        const assessments = response.data || [];
        setAssessments(assessments);
        setError(null);
      } catch (err) {
        console.error('Error fetching assessments:', err);
        setError('Failed to load assessments. Please try again later.');
        setAssessments([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchAssessments();
  }, [statusFilter, userId, teamId]);



  const filteredAssessments = assessments.filter(assessment => {
    const matchesStatus = statusFilter === 'all' || assessment.status === statusFilter;
    const matchesSearch =
      (assessment.employeeName || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (assessment.templateName || '').toLowerCase().includes(searchTerm.toLowerCase());

    return matchesStatus && matchesSearch;
  });

  if (isLoading) {
    return <div className="flex justify-center items-center h-64">
      <div className="text-center">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
        <p className="mt-2">Loading assessments...</p>
      </div>
    </div>;
  }

  if (error) {
    return <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
      <p>{error}</p>
      <button
        className="mt-2 bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
        onClick={() => window.location.reload()}
      >
        Retry
      </button>
    </div>;
  }

  return (
    <div className="p-6">
      <div className="flex flex-col sm:flex-row justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-4 sm:mb-0">Performance Assessments</h2>

        {/* Only managers and HR can create new assessments */}
        {(userRole === UserRole.HR_ADMIN || userRole === UserRole.MANAGER) && (
          <Link
            to="/assessments/create"
            className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            aria-label="Create new assessment"
          >
            <span className="material-icons mr-1" aria-hidden="true">add</span>
            New Assessment
          </Link>
        )}
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow p-4 mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-center">
          <div className="w-full sm:w-64 mb-4 sm:mb-0">
            <input
              type="text"
              placeholder="Search assessments..."
              className="border border-gray-300 rounded-md px-4 py-2 w-full"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <div className="flex gap-2">
            <button
              onClick={() => setStatusFilter('all')}
              className={getAssessmentFilterColor('all', statusFilter)}
              aria-pressed={statusFilter === 'all'}
              aria-label="Filter by all assessments"
            >
              All
            </button>
            <button
              onClick={() => setStatusFilter(AssessmentStatus.DRAFT)}
              className={getAssessmentFilterColor(AssessmentStatus.DRAFT, statusFilter)}
              aria-pressed={statusFilter === AssessmentStatus.DRAFT}
              aria-label="Filter by draft assessments"
            >
              Draft
            </button>
            <button
              onClick={() => setStatusFilter(AssessmentStatus.IN_PROGRESS)}
              className={getAssessmentFilterColor(AssessmentStatus.IN_PROGRESS, statusFilter)}
              aria-pressed={statusFilter === AssessmentStatus.IN_PROGRESS}
              aria-label="Filter by in-progress assessments"
            >
              In Progress
            </button>
            <button
              onClick={() => setStatusFilter(AssessmentStatus.PENDING_REVIEW)}
              className={getAssessmentFilterColor(AssessmentStatus.PENDING_REVIEW, statusFilter)}
              aria-pressed={statusFilter === AssessmentStatus.PENDING_REVIEW}
              aria-label="Filter by pending review assessments"
            >
              Pending Review
            </button>
            <button
              onClick={() => setStatusFilter(AssessmentStatus.COMPLETED)}
              className={getAssessmentFilterColor(AssessmentStatus.COMPLETED, statusFilter)}
              aria-pressed={statusFilter === AssessmentStatus.COMPLETED}
              aria-label="Filter by completed assessments"
            >
              Completed
            </button>
          </div>
        </div>
      </div>

      {/* Assessment List */}
      {filteredAssessments.length === 0 ? (
        <div className="bg-white rounded-lg shadow p-8 text-center">
          <p className="text-gray-500">No assessments found matching your criteria.</p>
          {(userRole === UserRole.HR_ADMIN || userRole === UserRole.MANAGER) && (
            <Link
              to="/assessments/create"
              className="mt-4 inline-block bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg"
            >
              Create New Assessment
            </Link>
          )}
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Employee
                </th>
                {userRole !== UserRole.EMPLOYEE && (
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Manager
                  </th>
                )}
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Template
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Score
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Due Date
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>

            <tbody className="bg-white divide-y divide-gray-200">
              {filteredAssessments.map(assessment => (
                <tr key={assessment.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                          <span className="text-sm font-medium">
                            {(assessment.employeeName || '').split(' ').map(n => n[0]).join('')}
                          </span>
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {assessment.employeeName}
                        </div>
                      </div>
                    </div>
                  </td>

                  {userRole !== UserRole.EMPLOYEE && (
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{assessment.managerName}</div>
                    </td>
                  )}

                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{assessment.templateName}</div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getAssessmentStatusColor(assessment.status)}`}>
                      {formatAssessmentStatus(assessment.status)}
                    </span>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {assessment.score ? assessment.score.toFixed(1) : '-'}
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {new Date(assessment.dueDate).toLocaleDateString()}
                    </div>
                    {isAssessmentOverdue(assessment) && (
                      <span className="text-xs text-red-600">Overdue</span>
                    )}
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <Link
                      to={`/assessments/${assessment.id}`}
                      className="text-blue-600 hover:text-blue-900 mr-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded"
                      aria-label={`View assessment for ${assessment.employeeName}`}
                    >
                      View
                    </Link>

                    {/* Only show edit for drafts or in-progress assessments */}
                    {(assessment.status === AssessmentStatus.DRAFT || assessment.status === AssessmentStatus.IN_PROGRESS) && (
                      <Link
                        to={`/assessments/${assessment.id}/edit`}
                        className="text-blue-600 hover:text-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded"
                        aria-label={`Edit assessment for ${assessment.employeeName}`}
                      >
                        Edit
                      </Link>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default AssessmentList;
