import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  PerformanceAssessment,
  AssessmentTemplate,
  AssessmentStatus,
  User,
  UserRole,
  AssessmentArea,
  AssessmentResponse
} from '../../types';
import api from '../../services/api';
import { ApiService } from '../../services/api';

interface AssessmentFormProps {
  assessmentId?: number;
  userRole: UserRole;
  userId: number;
  onSave?: (assessment: PerformanceAssessment) => void;
  onCancel?: () => void;
}

const AssessmentForm: React.FC<AssessmentFormProps> = ({
  assessmentId,
  userRole,
  userId,
  onSave,
  onCancel
}) => {
  const navigate = useNavigate();
  const isCreating = !assessmentId;

  // Form states
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Data states
  const [templates, setTemplates] = useState<AssessmentTemplate[]>([]);
  const [employees, setEmployees] = useState<User[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<AssessmentTemplate | null>(null);
  const [selectedEmployee, setSelectedEmployee] = useState<User | null>(null);
  const [dueDate, setDueDate] = useState<string>('');
  const [responses, setResponses] = useState<AssessmentResponse[]>([]);
  const [notes, setNotes] = useState<string>('');

  // Load data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);

        // 🔐 NIS2-COMPLIANT: Use centralized API service for real data
        const [templatesResponse, usersResponse] = await Promise.all([
          ApiService.getAssessmentTemplates(),
          ApiService.getUsers()
        ]);

        const templates = Array.isArray(templatesResponse.data) ? templatesResponse.data : [];
        const users = Array.isArray(usersResponse) ? usersResponse : [];

        // Filter active employees for assessment
        const employees = users.filter(user =>
          user.isActive &&
          (user.role === UserRole.EMPLOYEE ||
           user.role === UserRole.ENGINEER ||
           user.role === UserRole.SENIOR_ENGINEER ||
           user.role === UserRole.JUNIOR_ENGINEER)
        ).map(user => ({
          ...user,
          name: `${user.firstName} ${user.lastName}`
        }));

        setTemplates(templates);
        setEmployees(employees);

        // If editing an existing assessment, fetch its details
        if (assessmentId) {
          // 🔐 NIS2-COMPLIANT: Use centralized API service for real data
          const assessmentResponse = await ApiService.getAssessment(assessmentId);
          const assessment = assessmentResponse.data || assessmentResponse;

          if (assessment) {
            const foundTemplate = templates.find(t => t.id === assessment.templateId) || null;
            const foundEmployee = employees.find(e => e.id === assessment.employeeId) || null;

            setSelectedTemplate(foundTemplate);
            setSelectedEmployee(foundEmployee);
            setDueDate(assessment.dueDate ? assessment.dueDate.split('T')[0] : ''); // Format as YYYY-MM-DD
            setResponses(assessment.responses || []);
            setNotes(assessment.notes || '');
          }
        } else {
          // Set default due date (14 days from today)
          const defaultDueDate = new Date();
          defaultDueDate.setDate(defaultDueDate.getDate() + 14);
          setDueDate(defaultDueDate.toISOString().split('T')[0]);
        }

        setIsLoading(false);
      } catch (err) {
        // 🔐 NIS2-COMPLIANT: Enhanced error logging for security audit
        console.error('Error loading form data:', {
          error: err,
          userId,
          assessmentId,
          timestamp: new Date().toISOString(),
          action: 'LOAD_ASSESSMENT_FORM_DATA'
        });
        setError('Failed to load form data. Please try again later.');
        setIsLoading(false);
      }
    };

    fetchData();
  }, [assessmentId, userId]);

  // When template changes, reset responses
  useEffect(() => {
    if (selectedTemplate && isCreating) {
      // Initialize responses based on template areas
      const initialResponses: AssessmentResponse[] = selectedTemplate.areas.map(area => ({
        areaId: area.orderIndex,
        areaName: area.name,
        score: null,
        notes: ''
      }));

      setResponses(initialResponses);
    }
  }, [selectedTemplate, isCreating]);

  const handleScoreChange = (index: number, value: string) => {
    const score = parseInt(value);

    if (isNaN(score) || score < 0 || score > 100) {
      return; // Invalid score
    }

    const updatedResponses = [...responses];
    updatedResponses[index] = {
      ...updatedResponses[index],
      score
    };

    setResponses(updatedResponses);
  };

  const handleNotesChange = (index: number, value: string) => {
    const updatedResponses = [...responses];
    updatedResponses[index] = {
      ...updatedResponses[index],
      notes: value
    };

    setResponses(updatedResponses);
  };

  const calculateTotalScore = (): number | null => {
    if (!selectedTemplate || responses.length === 0) {
      return null;
    }

    let totalScore = 0;
    let totalWeight = 0;

    responses.forEach((response, index) => {
      if (response.score !== null && selectedTemplate.areas[index]) {
        const area = selectedTemplate.areas[index];
        totalScore += response.score * area.weight;
        totalWeight += area.weight;
      }
    });

    if (totalWeight === 0) {
      return null;
    }

    return parseFloat((totalScore / totalWeight).toFixed(1));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedTemplate || !selectedEmployee || !dueDate) {
      setError('Please fill in all required fields.');
      return;
    }

    // Check if all areas have scores
    const missingScores = responses.some(res => res.score === null);

    try {
      setIsSaving(true);
      setError(null);

      // Determine status based on completeness and submission action
      const status = missingScores ? AssessmentStatus.IN_PROGRESS : AssessmentStatus.COMPLETED;

      // Create assessment object
      const assessment: PerformanceAssessment = {
        id: assessmentId,
        employeeId: selectedEmployee.id,
        employeeName: selectedEmployee.name,
        managerId: userId,
        managerName: 'Current Manager', // Would be filled by backend
        templateId: selectedTemplate.id!,
        templateName: selectedTemplate.name,
        status,
        score: calculateTotalScore(),
        assessmentDate: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        submittedAt: status === AssessmentStatus.COMPLETED ? new Date().toISOString() : null,
        dueDate: new Date(dueDate).toISOString(),
        responses,
        notes
      };

      // 🔐 NIS2-COMPLIANT: Use centralized API service for real data
      let savedAssessment;
      if (assessmentId) {
        savedAssessment = await ApiService.updateAssessment(assessmentId, assessment);
        // 🔐 NIS2-COMPLIANT: Audit log for assessment update
        console.info('Assessment updated:', {
          assessmentId,
          userId,
          employeeId: selectedEmployee.id,
          status: assessment.status,
          timestamp: new Date().toISOString(),
          action: 'UPDATE_ASSESSMENT'
        });
      } else {
        savedAssessment = await ApiService.createAssessment(assessment);
        // 🔐 NIS2-COMPLIANT: Audit log for assessment creation
        console.info('Assessment created:', {
          userId,
          employeeId: selectedEmployee.id,
          templateId: selectedTemplate.id,
          status: assessment.status,
          timestamp: new Date().toISOString(),
          action: 'CREATE_ASSESSMENT'
        });
      }

      // Success handling
      if (onSave) {
        onSave(savedAssessment.data || savedAssessment);
      } else {
        // Navigate to the assessment list page
        navigate('/assessments');
      }
      setIsSaving(false);
    } catch (err: any) {
      // 🔐 NIS2-COMPLIANT: Enhanced error logging for security audit
      console.error('Error saving assessment:', {
        error: err,
        userId,
        assessmentId,
        employeeId: selectedEmployee?.id,
        templateId: selectedTemplate?.id,
        timestamp: new Date().toISOString(),
        action: 'SAVE_ASSESSMENT_ERROR'
      });
      setError(err.response?.data?.message || 'Failed to save assessment. Please try again.');
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      navigate('/assessments');
    }
  };

  const handleSaveAsDraft = async () => {
    try {
      setIsSaving(true);
      setError(null);

      // Create assessment object with DRAFT status
      const assessment: PerformanceAssessment = {
        id: assessmentId,
        employeeId: selectedEmployee?.id || 0,
        employeeName: selectedEmployee?.name || '',
        managerId: userId,
        managerName: 'Current Manager', // Would be filled by backend
        templateId: selectedTemplate?.id || 0,
        templateName: selectedTemplate?.name || '',
        status: AssessmentStatus.DRAFT,
        score: null,
        assessmentDate: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        submittedAt: null,
        dueDate: new Date(dueDate).toISOString(),
        responses,
        notes
      };

      // 🔐 NIS2-COMPLIANT: Use centralized API service for real data
      let savedAssessment;
      if (assessmentId) {
        savedAssessment = await ApiService.updateAssessment(assessmentId, assessment);
        // 🔐 NIS2-COMPLIANT: Audit log for draft update
        console.info('Assessment draft updated:', {
          assessmentId,
          userId,
          employeeId: selectedEmployee?.id,
          timestamp: new Date().toISOString(),
          action: 'UPDATE_DRAFT'
        });
      } else {
        savedAssessment = await ApiService.createAssessment(assessment);
        // 🔐 NIS2-COMPLIANT: Audit log for draft creation
        console.info('Assessment draft created:', {
          userId,
          employeeId: selectedEmployee?.id,
          templateId: selectedTemplate?.id,
          timestamp: new Date().toISOString(),
          action: 'CREATE_DRAFT'
        });
      }

      // Success handling
      if (onSave) {
        onSave(savedAssessment.data || savedAssessment);
      } else {
        // Navigate to the assessment list page
        navigate('/assessments');
      }
      setIsSaving(false);
    } catch (err: any) {
      // 🔐 NIS2-COMPLIANT: Enhanced error logging for security audit
      console.error('Error saving assessment draft:', {
        error: err,
        userId,
        assessmentId,
        employeeId: selectedEmployee?.id,
        templateId: selectedTemplate?.id,
        timestamp: new Date().toISOString(),
        action: 'SAVE_DRAFT_ERROR'
      });
      setError(err.response?.data?.message || 'Failed to save assessment draft. Please try again.');
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return <div className="flex justify-center items-center h-64">
      <div className="text-center">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
        <p className="mt-2">Loading assessment form...</p>
      </div>
    </div>;
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-xl font-semibold mb-6">
        {assessmentId ? 'Edit Performance Assessment' : 'Create Performance Assessment'}
      </h2>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit}>
        {/* Assessment Setup Section */}
        <div className="mb-8">
          <h3 className="text-lg font-medium text-gray-700 mb-4">Assessment Setup</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Employee Selection - Only editable during creation */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Employee*
              </label>
              <select
                value={selectedEmployee?.id || ''}
                onChange={(e) => {
                  const employeeId = parseInt(e.target.value);
                  const employee = employees.find(emp => emp.id === employeeId) || null;
                  setSelectedEmployee(employee);
                }}
                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
                disabled={!isCreating}
                required
              >
                <option value="">Select an employee</option>
                {employees.map(employee => (
                  <option key={employee.id} value={employee.id}>
                    {employee.name} ({employee.email})
                  </option>
                ))}
              </select>
            </div>

            {/* Template Selection - Only editable during creation */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Assessment Template*
              </label>
              <select
                value={selectedTemplate?.id || ''}
                onChange={(e) => {
                  const templateId = parseInt(e.target.value);
                  const template = templates.find(t => t.id === templateId) || null;
                  setSelectedTemplate(template);
                }}
                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
                disabled={!isCreating}
                required
              >
                <option value="">Select a template</option>
                {templates.map(template => (
                  <option key={template.id} value={template.id}>
                    {template.name}
                  </option>
                ))}
              </select>
              {selectedTemplate && (
                <p className="mt-1 text-sm text-gray-500">
                  {selectedTemplate.description}
                </p>
              )}
            </div>

            {/* Due Date */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Due Date*
              </label>
              <input
                type="date"
                value={dueDate}
                onChange={(e) => setDueDate(e.target.value)}
                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
                required
              />
            </div>
          </div>
        </div>

        {/* Assessment Areas Section */}
        {selectedTemplate && responses.length > 0 && (
          <div className="mb-8">
            <h3 className="text-lg font-medium text-gray-700 mb-4">Performance Assessment</h3>

            {selectedTemplate.areas.map((area, index) => (
              <div key={index} className="mb-6 p-4 border border-gray-200 rounded-md">
                <div className="mb-3 flex justify-between items-center">
                  <h4 className="font-medium text-gray-800">
                    {area.name} <span className="text-sm text-gray-500">(Weight: {area.weight * 100}%)</span>
                  </h4>
                  <div className="flex items-center">
                    <span className="text-sm font-medium mr-2">Score:</span>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      value={responses[index]?.score ?? ''}
                      onChange={(e) => handleScoreChange(index, e.target.value)}
                      className="w-16 border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500 text-center"
                    />
                  </div>
                </div>

                {area.description && (
                  <p className="text-sm text-gray-600 mb-2">
                    {area.description}
                  </p>
                )}

                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Notes / Feedback
                </label>
                <textarea
                  value={responses[index]?.notes || ''}
                  onChange={(e) => handleNotesChange(index, e.target.value)}
                  className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  rows={3}
                />
              </div>
            ))}

            {/* Overall Score */}
            <div className="mt-4 p-4 bg-gray-50 rounded-md">
              <div className="flex justify-between items-center">
                <h4 className="font-medium text-gray-800">Overall Score</h4>
                <div className="text-xl font-bold text-blue-600">
                  {calculateTotalScore() !== null ? `${calculateTotalScore()}%` : 'N/A'}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Overall Notes */}
        <div className="mb-8">
          <h3 className="text-lg font-medium text-gray-700 mb-4">Overall Notes</h3>
          <textarea
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
            rows={4}
            placeholder="Enter overall assessment notes, feedback, or action items..."
          />
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={handleCancel}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>

          <button
            type="button"
            onClick={handleSaveAsDraft}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            disabled={isSaving}
          >
            Save as Draft
          </button>

          <button
            type="submit"
            className={`px-4 py-2 rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${isSaving ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            disabled={isSaving}
          >
            {isSaving ? 'Saving...' : (
              assessmentId ? 'Update Assessment' : 'Create Assessment'
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default AssessmentForm;
