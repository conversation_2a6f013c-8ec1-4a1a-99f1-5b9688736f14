import React from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Avatar,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Assessment as AssessmentIcon,
  TrendingUp as TrendingUpIcon,
  Group as GroupIcon,
  EmojiEvents as TrophyIcon,
} from '@mui/icons-material';

const SimpleAnalyticsDashboard: React.FC = () => {
  const widgets = [
    {
      title: 'Performance Overview',
      icon: <AssessmentIcon />,
      color: 'primary.main',
      description: 'Individual, team, and organization performance metrics',
      value: '8.5',
      subtitle: 'Average Score'
    },
    {
      title: 'Engagement Trends',
      icon: <TrendingUpIcon />,
      color: 'success.main',
      description: 'Employee engagement trends over time',
      value: '↗ 12%',
      subtitle: 'Trending Up'
    },
    {
      title: 'Team Performance',
      icon: <GroupIcon />,
      color: 'info.main',
      description: 'Team member performance tracking',
      value: '8.2',
      subtitle: 'Team Average'
    },
    {
      title: 'Recognition Activity',
      icon: <TrophyIcon />,
      color: 'warning.main',
      description: 'Recent recognition and badge activity',
      value: '24',
      subtitle: 'This Month'
    },
  ];

  return (
    <Box sx={{ p: 3 }}>
      {/* Dashboard Header */}
      <Box display="flex" alignItems="center" mb={3}>
        <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
          <DashboardIcon />
        </Avatar>
        <Box>
          <Typography variant="h4" gutterBottom>
            Analytics Dashboard
          </Typography>
          <Typography variant="subtitle1" color="textSecondary">
            Performance metrics, engagement trends, and AI insights
          </Typography>
        </Box>
      </Box>

      {/* Dashboard Content */}
      <Grid container spacing={3}>
        {widgets.map((widget, index) => (
          <Grid item xs={12} md={6} lg={3} key={index}>
            <Card sx={{ height: 200, display: 'flex', flexDirection: 'column' }}>
              <CardContent sx={{ flex: 1 }}>
                <Box display="flex" alignItems="center" mb={2}>
                  <Avatar sx={{ bgcolor: widget.color, mr: 2 }}>
                    {widget.icon}
                  </Avatar>
                  <Typography variant="h6" component="div">
                    {widget.title}
                  </Typography>
                </Box>
                
                <Typography variant="h3" color={widget.color} gutterBottom>
                  {widget.value}
                </Typography>
                
                <Typography variant="body2" color="textSecondary" gutterBottom>
                  {widget.subtitle}
                </Typography>
                
                <Typography variant="caption" color="textSecondary">
                  {widget.description}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}

        {/* Additional Info Cards */}
        <Grid item xs={12} md={8}>
          <Card sx={{ height: 300 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                📊 Analytics Overview
              </Typography>
              <Typography variant="body2" color="textSecondary" paragraph>
                Welcome to the EHRX Analytics Dashboard! This comprehensive dashboard provides insights into:
              </Typography>
              <Box component="ul" sx={{ pl: 2 }}>
                <Typography component="li" variant="body2" gutterBottom>
                  <strong>Performance Metrics:</strong> Track individual, team, and organizational performance
                </Typography>
                <Typography component="li" variant="body2" gutterBottom>
                  <strong>Engagement Analytics:</strong> Monitor employee engagement trends and satisfaction
                </Typography>
                <Typography component="li" variant="body2" gutterBottom>
                  <strong>Recognition System:</strong> View badge awards and peer recognition activity
                </Typography>
                <Typography component="li" variant="body2" gutterBottom>
                  <strong>AI Insights:</strong> Get predictive analytics and recommendations
                </Typography>
              </Box>
              <Box 
                sx={{ 
                  mt: 2, 
                  p: 2, 
                  bgcolor: 'info.light', 
                  borderRadius: 1,
                  color: 'info.contrastText'
                }}
              >
                <Typography variant="body2">
                  🚀 <strong>Status:</strong> All backend APIs are functional and ready. 
                  Advanced widgets with real-time data visualization coming soon!
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card sx={{ height: 300 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                🎯 Quick Actions
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Performance Reviews
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Manage employee assessments and reviews
                  </Typography>
                </Box>
                <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Team Analytics
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    View detailed team performance metrics
                  </Typography>
                </Box>
                <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Recognition Center
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Give recognition and manage badges
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default SimpleAnalyticsDashboard;
