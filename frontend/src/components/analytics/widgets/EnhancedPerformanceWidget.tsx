import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  CircularProgress,
  Alert,
  Grid,
  Chip,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  LinearProgress,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  Tooltip,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  People as PeopleIcon,
  Assessment as AssessmentIcon,
  Star as StarIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import { ApiService } from '../../../services/api';
import { getPerformanceColor, handleAnalyticsError } from '../../../utils/analytics';

interface PerformanceData {
  overview: {
    totalEmployees: number;
    employeesWithAssessments: number;
    overallAverage: number;
    assessmentParticipation: number;
  };
  performanceDistribution: {
    excellent: number;
    good: number;
    satisfactory: number;
    needsImprovement: number;
  };
  departmentAverages: Array<{
    department: string;
    averageScore: number;
    employeeCount: number;
  }>;
  topPerformers: Array<{
    id: number;
    name: string;
    role: string;
    department: string;
    avgScore: number;
  }>;
  improvementOpportunities: Array<{
    id: number;
    name: string;
    role: string;
    department: string;
    avgScore: number;
  }>;
}

interface EnhancedPerformanceWidgetProps {
  filters?: {
    teamId?: number;
    role?: string;
    departmentId?: number;
    period?: string;
  };
}

const EnhancedPerformanceWidget: React.FC<EnhancedPerformanceWidgetProps> = ({ filters }) => {
  const [data, setData] = useState<PerformanceData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [period, setPeriod] = useState(filters?.period || '3months');
  const [selectedView, setSelectedView] = useState<'overview' | 'departments' | 'performers'>('overview');

  useEffect(() => {
    loadPerformanceData();
  }, [period, filters]);

  const loadPerformanceData = async () => {
    try {
      setLoading(true);
      setError(null);

      // 🔐 NIS2-COMPLIANT: Use real data from centralized API
      const response = await ApiService.getPerformanceAnalytics({
        ...filters,
        period
      });

      if (response && response.success) {
        setData(response.data);
      } else {
        throw new Error(response?.error || 'Failed to load performance data');
      }
    } catch (err) {
      setError('Failed to load performance analytics');
      console.error('Error loading performance data:', err);
      
      // Fallback data structure
      setData({
        overview: {
          totalEmployees: 0,
          employeesWithAssessments: 0,
          overallAverage: 0,
          assessmentParticipation: 0
        },
        performanceDistribution: {
          excellent: 0,
          good: 0,
          satisfactory: 0,
          needsImprovement: 0
        },
        departmentAverages: [],
        topPerformers: [],
        improvementOpportunities: []
      });
    } finally {
      setLoading(false);
    }
  };



  const getPerformanceLevel = (score: number) => {
    if (score >= 90) return 'Excellent';
    if (score >= 75) return 'Good';
    if (score >= 60) return 'Satisfactory';
    return 'Needs Improvement';
  };

  if (loading) {
    return (
      <Card>
        <CardContent>
          <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
            <CircularProgress />
          </Box>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent>
          <Alert severity="error">{error}</Alert>
        </CardContent>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card>
        <CardContent>
          <Alert severity="info">No performance data available</Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6" component="h3">
            <AssessmentIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            Performance Analytics
          </Typography>
          
          <Box display="flex" gap={1}>
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Period</InputLabel>
              <Select
                value={period}
                label="Period"
                onChange={(e) => setPeriod(e.target.value)}
              >
                <MenuItem value="1month">1 Month</MenuItem>
                <MenuItem value="3months">3 Months</MenuItem>
                <MenuItem value="6months">6 Months</MenuItem>
                <MenuItem value="1year">1 Year</MenuItem>
              </Select>
            </FormControl>
            
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>View</InputLabel>
              <Select
                value={selectedView}
                label="View"
                onChange={(e) => setSelectedView(e.target.value as any)}
              >
                <MenuItem value="overview">Overview</MenuItem>
                <MenuItem value="departments">Departments</MenuItem>
                <MenuItem value="performers">Top Performers</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </Box>

        {selectedView === 'overview' && (
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Box p={2} bgcolor="background.paper" borderRadius={1}>
                <Typography variant="subtitle2" color="textSecondary">
                  Overall Average Score
                </Typography>
                <Typography variant="h4" color={getPerformanceColor(data.overview.overallAverage)}>
                  {data.overview.overallAverage.toFixed(1)}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  {getPerformanceLevel(data.overview.overallAverage)}
                </Typography>
              </Box>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Box p={2} bgcolor="background.paper" borderRadius={1}>
                <Typography variant="subtitle2" color="textSecondary">
                  Assessment Participation
                </Typography>
                <Typography variant="h4" color="primary">
                  {data.overview.assessmentParticipation}%
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  {data.overview.employeesWithAssessments} of {data.overview.totalEmployees} employees
                </Typography>
              </Box>
            </Grid>
            
            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom>
                Performance Distribution
              </Typography>
              <Grid container spacing={1}>
                <Grid item xs={3}>
                  <Chip
                    label={`Excellent: ${data.performanceDistribution.excellent}`}
                    color="success"
                    variant="outlined"
                    size="small"
                  />
                </Grid>
                <Grid item xs={3}>
                  <Chip
                    label={`Good: ${data.performanceDistribution.good}`}
                    color="primary"
                    variant="outlined"
                    size="small"
                  />
                </Grid>
                <Grid item xs={3}>
                  <Chip
                    label={`Satisfactory: ${data.performanceDistribution.satisfactory}`}
                    color="warning"
                    variant="outlined"
                    size="small"
                  />
                </Grid>
                <Grid item xs={3}>
                  <Chip
                    label={`Needs Improvement: ${data.performanceDistribution.needsImprovement}`}
                    color="error"
                    variant="outlined"
                    size="small"
                  />
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        )}

        {selectedView === 'departments' && (
          <Box>
            <Typography variant="subtitle2" gutterBottom>
              Department Performance Comparison
            </Typography>
            <List dense>
              {data.departmentAverages.slice(0, 5).map((dept, index) => (
                <React.Fragment key={dept.department}>
                  <ListItem>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: getPerformanceColor(dept.averageScore) }}>
                        {index + 1}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={dept.department}
                      secondary={`${dept.employeeCount} employees`}
                    />
                    <Box textAlign="right">
                      <Typography variant="h6" color={getPerformanceColor(dept.averageScore)}>
                        {dept.averageScore.toFixed(1)}
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={dept.averageScore}
                        sx={{
                          width: 60,
                          '& .MuiLinearProgress-bar': {
                            backgroundColor: getPerformanceColor(dept.averageScore)
                          }
                        }}
                      />
                    </Box>
                  </ListItem>
                  {index < data.departmentAverages.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          </Box>
        )}

        {selectedView === 'performers' && (
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom>
                <StarIcon sx={{ mr: 1, verticalAlign: 'middle', color: 'gold' }} />
                Top Performers
              </Typography>
              <List dense>
                {data.topPerformers.slice(0, 5).map((performer, index) => (
                  <ListItem key={performer.id}>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: '#4caf50' }}>
                        <TrendingUpIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={performer.name}
                      secondary={`${performer.role} • ${performer.department}`}
                    />
                    <Tooltip title={`Score: ${performer.avgScore.toFixed(1)}`}>
                      <Chip
                        label={performer.avgScore.toFixed(1)}
                        color="success"
                        size="small"
                      />
                    </Tooltip>
                  </ListItem>
                ))}
              </List>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom>
                <WarningIcon sx={{ mr: 1, verticalAlign: 'middle', color: 'orange' }} />
                Improvement Opportunities
              </Typography>
              <List dense>
                {data.improvementOpportunities.slice(0, 5).map((opportunity) => (
                  <ListItem key={opportunity.id}>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: '#ff9800' }}>
                        <TrendingDownIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={opportunity.name}
                      secondary={`${opportunity.role} • ${opportunity.department}`}
                    />
                    <Tooltip title={`Score: ${opportunity.avgScore.toFixed(1)}`}>
                      <Chip
                        label={opportunity.avgScore.toFixed(1)}
                        color="warning"
                        size="small"
                      />
                    </Tooltip>
                  </ListItem>
                ))}
              </List>
            </Grid>
          </Grid>
        )}
      </CardContent>
    </Card>
  );
};

export default EnhancedPerformanceWidget;
