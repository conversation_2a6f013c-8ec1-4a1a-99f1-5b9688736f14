// User-related types
export enum UserRole {
  HR_ADMIN = 'hr_admin',
  MANAGER = 'manager',
  EMPLOYEE = 'employee',
  ENGINEER = 'engineer',
  SENIOR_ENGINEER = 'senior_engineer',
  JUNIOR_ENGINEER = 'junior_engineer',
  GUEST = 'guest'
}

export interface User {
  id: number;
  email: string;
  firstName: string;
  lastName: string;
  name?: string; // Computed property: firstName + lastName
  role: UserRole | string;
  title?: string;
  phone?: string;
  location?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  mustChangePassword?: boolean;
  twoFactorEnabled?: boolean;
  lastLoginAt?: string;
  accountStatus?: string;
  organizationalUnitId?: number;
  managerId?: number;
  isActive?: boolean;
  hireDate?: string; // Add hireDate property
  teamId?: number; // Add teamId property
  profileImage?: string; // Add profileImage property
  // Additional properties for Settings functionality
  passwordChangedAt?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
}

// Assessment template types
export enum RuleType {
  ADDITION = 'addition',
  SUBTRACTION = 'subtraction',
  MULTIPLICATION = 'multiplication',
  DIVISION = 'division',
  CONDITIONAL = 'conditional',
  THRESHOLD = 'threshold'
}

export enum ConditionOperator {
  EQUALS = 'equals',
  NOT_EQUALS = 'not_equals',
  GREATER_THAN = 'greater_than',
  LESS_THAN = 'less_than',
  GREATER_THAN_OR_EQUAL = 'greater_than_or_equal',
  LESS_THAN_OR_EQUAL = 'less_than_or_equal',
  GREATER_OR_EQUAL = 'greater_or_equal', // Alias
  LESS_OR_EQUAL = 'less_or_equal', // Alias
  CONTAINS = 'contains',
  NOT_CONTAINS = 'not_contains'
}

export interface ScoringRule {
  id?: number;
  ruleType: RuleType;
  conditionField?: string;
  conditionOperator?: ConditionOperator;
  conditionValue?: string;
  scoreAdjustment: number;
  description?: string;
  threshold?: number; // For threshold-based rules
}

export interface AssessmentArea {
  id?: number;
  name: string;
  description?: string;
  weight: number;
  maxScore: number;
  orderIndex: number;
  scoringRules?: ScoringRule[];
}

export interface AssessmentTemplate {
  id?: number;
  name: string;
  description?: string;
  createdById?: number;
  isActive?: boolean;
  version?: number;
  parentTemplateId?: number;
  isGlobal?: boolean;
  areas: AssessmentArea[];
  createdAt?: string;
  updatedAt?: string;
}

// Assessment instance types
export enum AssessmentStatus {
  DRAFT = 'draft',
  IN_PROGRESS = 'in_progress',
  PENDING_REVIEW = 'pending_review',
  COMPLETED = 'completed',
  APPROVED = 'approved',
  REJECTED = 'rejected',
}

export interface ScoreAdjustment {
  ruleId: number;
  ruleType: RuleType;
  adjustment: number;
  reason: string;
}

export interface AssessmentResponse {
  id?: number;
  areaId: number;
  areaName?: string;
  score: number | null; // Allow null for initial state
  baseScore?: number;
  evaluatorComments?: string;
  employeeComments?: string;
  notes?: string; // Add notes property
  areaWeight?: number;
  weightedScore?: number;
  scoreAdjustments?: ScoreAdjustment[];
  additionalData?: Record<string, any>;
}

export interface Assessment {
  id?: number;
  templateId: number;
  templateName?: string;
  employeeId: number;
  employeeName?: string;
  evaluatorId?: number;
  evaluatorName?: string;
  managerId?: number;
  managerName?: string;
  status: AssessmentStatus;
  assessmentDate: string;
  dueDate?: string;
  notes?: string;
  totalScore?: number | null;
  score?: number | null; // Alias for totalScore
  scorePercentage?: number;
  responses: AssessmentResponse[];
  createdAt?: string;
  updatedAt?: string;
  submittedAt?: string | null; // Add submittedAt property
  // Additional properties for enhanced functionality
  employee?: User;
  evaluator?: User;
  template?: AssessmentTemplate;
}

// Additional assessment interfaces for compatibility
export interface PerformanceAssessment extends Assessment {
  // All Assessment properties are inherited including submittedAt
}

export interface AssessmentInstance extends Assessment {
  // Alias for Assessment interface with template included
  template?: AssessmentTemplate;
}

// Database and Settings interfaces
export interface DatabaseTable {
  name: string;
  rowCount: number;
  columns: string[];
  description?: string;
}

export interface TableData {
  records: any[];
  columns: any[];
  page?: number;
  pageSize?: number;
}

export interface ApiEndpoint {
  id: string;
  path: string;
  method: string;
  description?: string;
  isActive: boolean;
}

export interface ApiKey {
  id: string;
  name: string;
  key: string;
  permissions: string[];
  isActive: boolean;
  createdAt: string;
}

export interface RateLimitConfig {
  id: string;
  endpoint: string;
  limit: number;
  window: number;
  isActive: boolean;
}

export interface Role {
  id: string;
  name: string;
  permissions: string[];
  description?: string;
}

// Recent Activity interface for dashboard
export interface RecentActivity {
  id: number;
  type: 'assessment' | 'review' | 'goal' | 'feedback';
  title: string;
  description: string;
  timestamp: string;
  status: 'completed' | 'pending' | 'overdue';
}

// Assessment creation with advanced scoring
export interface AssessmentAreaInput {
  areaId: number;
  score: number;
  baseScore?: number;
  evaluatorComments?: string;
  employeeComments?: string;
  additionalData?: Record<string, any>;
  comments?: string;
}

export interface CreateAssessmentWithScoring {
  templateId: number;
  employeeId: number;
  evaluatorId?: number;
  assessmentDate: string;
  notes?: string;
  areaInputs: AssessmentAreaInput[];
  status?: string;
}

// Additional missing interfaces
export interface AnalyticsDashboard {
  id: string;
  name: string;
  description?: string;
  isDefault?: boolean;
  widgetConfig?: {
    widgets: Array<{
      type: string;
      position: { x: number; y: number };
    }>;
  };
  widgets?: any[];
}

export interface RecognitionInstance {
  id: string;
  title: string;
  description: string;
  timestamp: string;
  badge?: {
    name: string;
    badgeType: string;
  };
  giver?: {
    firstName: string;
    lastName: string;
  };
  receiver?: {
    firstName: string;
    lastName: string;
  };
  message?: string;
  givenAt: string;
  pointsAwarded: number;
}

// Common scoring data fields
export interface AssessmentScoringData {
  nc_incidents?: number;
  training_completed?: number;
  certifications?: number;
  projects_completed?: number;
  overtime_hours?: number;
  customer_complaints?: number;
  customer_compliments?: number;
  team_collaboration_score?: number;
  innovation_initiatives?: number;
  mentoring_hours?: number;
  safety_incidents?: number;
  quality_improvements?: number;
  attendance_score?: number;
  punctuality_score?: number;
  customFields?: Record<string, any>;
}

// Dashboard types
export interface OverviewStats {
  totalAssessments: number;
  statusCounts: { status: string; count: number }[];
  averageScore: number;
}

export interface EmployeePerformance {
  employeeId: number;
  firstName: string;
  lastName: string;
  averageScore: number;
  assessmentCount: number;
}

export interface AssessmentAreaStats {
  areaId: number;
  areaName: string;
  averageScore: number;
  averageWeightedScore: number;
}

export interface TrendData {
  year: number;
  month: number;
  count: number;
  averageScore: number;
}

export interface UserDashboard {
  pendingAssessments: number;
  completedAssessments: number;
  latestAssessment: Assessment | null;
  strengths: { areaId: number; areaName: string; averageScore: number }[];
  weaknesses: { areaId: number; areaName: string; averageScore: number }[];
}

// Organizational Unit interface
export interface OrganizationalUnit {
  id: number;
  name: string;
  description?: string;
  parentId?: number;
  managerId?: number;
  level: number;
  path?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  children?: OrganizationalUnit[];
  parent?: OrganizationalUnit;
  manager?: User;
  members?: User[];
  type?: 'organization' | 'division' | 'department' | 'team' | 'squad' | 'unit';
  budget?: number;
}
