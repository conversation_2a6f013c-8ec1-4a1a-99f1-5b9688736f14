import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UsersModule } from './users/users.module';
import { AuthModule } from './auth/auth.module';
import { SecurityModule } from './security/security.module';
import { TeamsModule } from './teams/teams.module';
import { AssessmentsModule } from './assessments/assessments.module';
import { DatabaseModule } from './database/database.module';
import { AnalyticsModule } from './analytics/analytics.module';
import { RecognitionModule } from './recognition/recognition.module';
import { SystemModule } from './system/system.module';
import { ApiModule } from './api/api.module';
import { AiModule } from './ai/ai.module';
import { RolesModule } from './roles/roles.module';
import { MonthlyDashboardsModule } from './monthly-dashboards/monthly-dashboards.module';
import { ReportingModule } from './reporting/reporting.module';
import { DashboardModule } from './dashboard/dashboard.module';
import { LoggingModule } from './modules/logging/logging.module';
import { User } from './users/entities/user.entity';
import { OrganizationalUnit } from './teams/entities/organizational-unit.entity';
import { Skillset } from './teams/entities/skillset.entity';
import { UserSkillset } from './teams/entities/user-skillset.entity';
import { TeamMember } from './teams/entities/team-member.entity';
import { AssessmentTemplate } from './assessments/entities/assessment-template.entity';
import { AssessmentArea } from './assessments/entities/assessment-area.entity';
import { AssessmentInstance } from './assessments/entities/assessment-instance.entity';
import { AssessmentResponse } from './assessments/entities/assessment-response.entity';
import { ScoringRule } from './assessments/entities/scoring-rule.entity';
// Management Dashboard Entities
import { AnalyticsDashboard } from './analytics/entities/analytics-dashboard.entity';
import { PerformanceMetric } from './analytics/entities/performance-metric.entity';
import { EngagementSurvey } from './analytics/entities/engagement-survey.entity';
import { SurveyResponse } from './analytics/entities/survey-response.entity';
import { RecognitionBadge } from './recognition/entities/recognition-badge.entity';
import { RecognitionInstance } from './recognition/entities/recognition-instance.entity';
import { UserGamification } from './recognition/entities/user-gamification.entity';
import { MicroFeedback } from './recognition/entities/micro-feedback.entity';
import { AttritionPrediction } from './ai/entities/attrition-prediction.entity';
import { CompetencyFramework } from './ai/entities/competency-framework.entity';
import { CareerPath } from './ai/entities/career-path.entity';
import { AiInsight } from './ai/entities/ai-insight.entity';
// Monthly Dashboard Entities
import { MonthlyDashboardSubmission } from './monthly-dashboards/entities/monthly-dashboard-submission.entity';
import { MonthlyDashboardKpi } from './monthly-dashboards/entities/monthly-dashboard-kpi.entity';
import { MonthlyDashboardKpiValue } from './monthly-dashboards/entities/monthly-dashboard-kpi-value.entity';
import { DatabaseOptimizationConfig } from './config/database-optimization.config';
import { MonthlyDashboardTeamTarget } from './monthly-dashboards/entities/monthly-dashboard-team-target.entity';
import { AuditLog } from './audit/entities/audit-log.entity';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        // Validate database configuration
        DatabaseOptimizationConfig.validateConfig();

        const baseConfig = {
          type: 'mysql' as const,
          host: configService.get('DB_HOST', 'localhost'),
          port: parseInt(configService.get('DB_PORT', '3306')),
          username: configService.get('DB_USERNAME', 'root'),
          password: configService.get('DB_PASSWORD', ''),
          database: configService.get('DB_NAME', 'ehrx'),
          entities: [
            // Core entities
            User,
            OrganizationalUnit,
            Skillset,
            UserSkillset,
            TeamMember,
            AssessmentTemplate,
            AssessmentArea,
            AssessmentInstance,
            AssessmentResponse,
            ScoringRule,
            // Management Dashboard entities
            AnalyticsDashboard,
            PerformanceMetric,
            EngagementSurvey,
            SurveyResponse,
            RecognitionBadge,
            RecognitionInstance,
            UserGamification,
            MicroFeedback,
            AttritionPrediction,
            CompetencyFramework,
            CareerPath,
            AiInsight,
            // Monthly Dashboard entities
            MonthlyDashboardSubmission,
            MonthlyDashboardKpi,
            MonthlyDashboardKpiValue,
            MonthlyDashboardTeamTarget,
            // Audit entities
            AuditLog,
          ],
          // 🔐 SECURITY: Always disable synchronize to prevent data loss and security risks
          synchronize: false,
          autoLoadEntities: true,
        };

        // Apply database optimizations
        return DatabaseOptimizationConfig.getOptimizedConfig(baseConfig);
      },
    }),
    UsersModule,
    AuthModule,
    SecurityModule,
    TeamsModule,
    AssessmentsModule,
    DatabaseModule,
    // Management Dashboard modules
    AnalyticsModule,
    RecognitionModule,
    SystemModule,
    ApiModule,
    AiModule,
    RolesModule,
    DashboardModule,
    MonthlyDashboardsModule,
    ReportingModule,
    LoggingModule,
  ],
})
export class AppModule { }
