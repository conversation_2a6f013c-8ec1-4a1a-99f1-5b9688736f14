import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AuditLog } from './entities/audit-log.entity';

// 🔐 NIS2-COMPLIANT: Comprehensive audit logging service
// Provides detailed audit trails for all dashboard data access and modifications

export interface AuditLogEntry {
  userId: number;
  action: string;
  resource: string;
  resourceId?: string;
  details?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'authentication' | 'authorization' | 'data_access' | 'data_modification' | 'system_config' | 'security_event';
  outcome: 'success' | 'failure' | 'error';
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
}

export interface SecurityEvent {
  type: 'login_attempt' | 'unauthorized_access' | 'data_breach' | 'suspicious_activity' | 'system_error';
  description: string;
  metadata?: Record<string, any>;
}

@Injectable()
export class AuditLoggingService {
  private readonly logger = new Logger(AuditLoggingService.name);

  constructor(
    @InjectRepository(AuditLog)
    private auditLogRepository: Repository<AuditLog>,
  ) {}

  /**
   * Log a general audit event
   */
  async logEvent(entry: AuditLogEntry): Promise<void> {
    try {
      const auditLog = this.auditLogRepository.create({
        userId: entry.userId,
        action: entry.action,
        resource: entry.resource,
        resourceId: entry.resourceId,
        details: entry.details,
        ipAddress: entry.ipAddress,
        userAgent: entry.userAgent,
        sessionId: entry.sessionId,
        severity: entry.severity,
        category: entry.category,
        outcome: entry.outcome,
        riskLevel: entry.riskLevel,
        timestamp: new Date(),
        compliance: {
          nis2: true,
          gdpr: true,
          retention_period: 2555 // 7 years in days
        }
      });

      await this.auditLogRepository.save(auditLog);

      // Log to application logger for immediate monitoring
      this.logger.log(
        `AUDIT: ${entry.action} on ${entry.resource} by user ${entry.userId} - ${entry.outcome}`,
        {
          userId: entry.userId,
          action: entry.action,
          resource: entry.resource,
          outcome: entry.outcome,
          severity: entry.severity,
          riskLevel: entry.riskLevel
        }
      );

      // Trigger alerts for high-risk events
      if (entry.riskLevel === 'critical' || entry.severity === 'critical') {
        await this.triggerSecurityAlert(entry);
      }

    } catch (error) {
      this.logger.error('Failed to log audit event', error);
      // Don't throw error to avoid disrupting main application flow
    }
  }

  /**
   * Log dashboard data access
   */
  async logDashboardAccess(
    userId: number,
    dashboardType: string,
    dataAccessed: string[],
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.logEvent({
      userId,
      action: 'dashboard_access',
      resource: 'dashboard',
      resourceId: dashboardType,
      details: {
        dashboard_type: dashboardType,
        data_accessed: dataAccessed,
        access_time: new Date().toISOString(),
        ...metadata
      },
      severity: 'low',
      category: 'data_access',
      outcome: 'success',
      riskLevel: 'low'
    });
  }

  /**
   * Log data modifications
   */
  async logDataModification(
    userId: number,
    resource: string,
    resourceId: string,
    action: 'create' | 'update' | 'delete',
    oldData?: Record<string, any>,
    newData?: Record<string, any>,
    metadata?: Record<string, any>
  ): Promise<void> {
    const riskLevel = action === 'delete' ? 'high' : 'medium';
    const severity = action === 'delete' ? 'high' : 'medium';

    await this.logEvent({
      userId,
      action: `${action}_${resource}`,
      resource,
      resourceId,
      details: {
        action,
        old_data: oldData,
        new_data: newData,
        modification_time: new Date().toISOString(),
        ...metadata
      },
      severity,
      category: 'data_modification',
      outcome: 'success',
      riskLevel
    });
  }

  /**
   * Log authentication events
   */
  async logAuthentication(
    userId: number | null,
    action: 'login' | 'logout' | 'login_failed' | 'token_refresh',
    ipAddress?: string,
    userAgent?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    const outcome = action.includes('failed') ? 'failure' : 'success';
    const riskLevel = action === 'login_failed' ? 'medium' : 'low';

    await this.logEvent({
      userId: userId || 0,
      action,
      resource: 'authentication',
      details: {
        action,
        timestamp: new Date().toISOString(),
        ...metadata
      },
      ipAddress,
      userAgent,
      severity: 'medium',
      category: 'authentication',
      outcome,
      riskLevel
    });
  }

  /**
   * Log authorization events
   */
  async logAuthorization(
    userId: number,
    resource: string,
    action: string,
    granted: boolean,
    reason?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    const outcome = granted ? 'success' : 'failure';
    const riskLevel = granted ? 'low' : 'medium';

    await this.logEvent({
      userId,
      action: `authorize_${action}`,
      resource,
      details: {
        requested_action: action,
        granted,
        reason,
        timestamp: new Date().toISOString(),
        ...metadata
      },
      severity: granted ? 'low' : 'medium',
      category: 'authorization',
      outcome,
      riskLevel
    });
  }

  /**
   * Log security events
   */
  async logSecurityEvent(
    userId: number | null,
    event: SecurityEvent,
    ipAddress?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    const riskLevel = this.assessSecurityEventRisk(event.type);
    const severity = riskLevel === 'critical' ? 'critical' : 'high';

    await this.logEvent({
      userId: userId || 0,
      action: `security_event_${event.type}`,
      resource: 'security',
      details: {
        event_type: event.type,
        description: event.description,
        event_metadata: event.metadata,
        timestamp: new Date().toISOString(),
        ...metadata
      },
      ipAddress,
      severity,
      category: 'security_event',
      outcome: 'success',
      riskLevel
    });
  }

  /**
   * Log system configuration changes
   */
  async logConfigurationChange(
    userId: number,
    configType: string,
    changes: Record<string, any>,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.logEvent({
      userId,
      action: 'config_change',
      resource: 'system_configuration',
      resourceId: configType,
      details: {
        config_type: configType,
        changes,
        timestamp: new Date().toISOString(),
        ...metadata
      },
      severity: 'high',
      category: 'system_config',
      outcome: 'success',
      riskLevel: 'medium'
    });
  }

  /**
   * Get audit logs with filtering
   */
  async getAuditLogs(
    filters: {
      userId?: number;
      resource?: string;
      action?: string;
      category?: string;
      severity?: string;
      startDate?: Date;
      endDate?: Date;
      limit?: number;
      offset?: number;
    } = {}
  ): Promise<{ logs: AuditLog[]; total: number }> {
    const query = this.auditLogRepository.createQueryBuilder('audit');

    if (filters.userId) {
      query.andWhere('audit.userId = :userId', { userId: filters.userId });
    }

    if (filters.resource) {
      query.andWhere('audit.resource = :resource', { resource: filters.resource });
    }

    if (filters.action) {
      query.andWhere('audit.action LIKE :action', { action: `%${filters.action}%` });
    }

    if (filters.category) {
      query.andWhere('audit.category = :category', { category: filters.category });
    }

    if (filters.severity) {
      query.andWhere('audit.severity = :severity', { severity: filters.severity });
    }

    if (filters.startDate) {
      query.andWhere('audit.timestamp >= :startDate', { startDate: filters.startDate });
    }

    if (filters.endDate) {
      query.andWhere('audit.timestamp <= :endDate', { endDate: filters.endDate });
    }

    query.orderBy('audit.timestamp', 'DESC');

    const total = await query.getCount();

    if (filters.limit) {
      query.limit(filters.limit);
    }

    if (filters.offset) {
      query.offset(filters.offset);
    }

    const logs = await query.getMany();

    return { logs, total };
  }

  /**
   * Generate compliance report
   */
  async generateComplianceReport(
    startDate: Date,
    endDate: Date
  ): Promise<{
    summary: Record<string, number>;
    criticalEvents: AuditLog[];
    dataAccessSummary: Record<string, number>;
    securityEvents: AuditLog[];
  }> {
    const { logs } = await this.getAuditLogs({ startDate, endDate });

    const summary = logs.reduce((acc, log) => {
      acc[log.category] = (acc[log.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const criticalEvents = logs.filter(log => 
      log.severity === 'critical' || log.riskLevel === 'critical'
    );

    const dataAccessSummary = logs
      .filter(log => log.category === 'data_access')
      .reduce((acc, log) => {
        acc[log.resource] = (acc[log.resource] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

    const securityEvents = logs.filter(log => log.category === 'security_event');

    return {
      summary,
      criticalEvents,
      dataAccessSummary,
      securityEvents
    };
  }

  /**
   * Assess security event risk level
   */
  private assessSecurityEventRisk(eventType: string): 'low' | 'medium' | 'high' | 'critical' {
    const riskMap = {
      'login_attempt': 'low',
      'unauthorized_access': 'high',
      'data_breach': 'critical',
      'suspicious_activity': 'medium',
      'system_error': 'medium'
    };

    return riskMap[eventType] as any || 'medium';
  }

  /**
   * Trigger security alert for critical events
   */
  private async triggerSecurityAlert(entry: AuditLogEntry): Promise<void> {
    this.logger.warn(
      `SECURITY ALERT: Critical event detected - ${entry.action} on ${entry.resource}`,
      {
        userId: entry.userId,
        action: entry.action,
        resource: entry.resource,
        severity: entry.severity,
        riskLevel: entry.riskLevel,
        details: entry.details
      }
    );

    // Here you would integrate with your alerting system
    // e.g., send email, Slack notification, SIEM integration, etc.
  }
}
