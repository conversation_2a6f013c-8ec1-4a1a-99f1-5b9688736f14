import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TeamsService } from './teams.service';
import { TeamsController } from './teams.controller';
import { OrganizationalUnitsService } from './organizational-units.service';
import { SkillsetsService } from './skillsets.service';
import { Team } from './entities/team.entity';
import { TeamMember } from './entities/team-member.entity';
import { OrganizationalUnit } from './entities/organizational-unit.entity';
import { Skillset } from './entities/skillset.entity';
import { UserSkillset } from './entities/user-skillset.entity';
import { User } from '../users/entities/user.entity';
// import { LoggingModule } from '../modules/logging/logging.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Team,
      TeamMember,
      OrganizationalUnit,
      Skillset,
      UserSkillset,
      User
    ]),
    // LoggingModule
  ],
  controllers: [TeamsController],
  providers: [TeamsService, OrganizationalUnitsService, SkillsetsService],
  exports: [TeamsService, OrganizationalUnitsService, SkillsetsService]
})
export class TeamsModule { }
