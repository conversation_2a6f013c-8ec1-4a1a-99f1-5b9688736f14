import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Connection } from 'typeorm';
import { OrganizationalUnit, OrganizationalUnitType } from './entities/organizational-unit.entity';
import { User, UserRole } from '../users/entities/user.entity';
// import { LoggingService } from '../modules/logging/logging.service';
// import { LogLevel, LogSource } from '../modules/logging/interfaces/log.interface';

export interface CreateOrganizationalUnitDto {
  name: string;
  type: OrganizationalUnitType;
  description?: string;
  parentId?: number;
  managerId?: number;
  budget?: number;
}

export interface UpdateOrganizationalUnitDto {
  name?: string;
  type?: OrganizationalUnitType;
  description?: string;
  managerId?: number;
  budget?: number;
}

@Injectable()
export class OrganizationalUnitsService {
  constructor(
    @InjectRepository(OrganizationalUnit)
    private orgUnitRepository: Repository<OrganizationalUnit>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private connection: Connection,
    // private loggingService: LoggingService,
  ) { }

  async create(createDto: CreateOrganizationalUnitDto, userId: number, userRole: string): Promise<OrganizationalUnit> {
    // Only HR_ADMIN, CEO, VP, and DIRECTOR roles can create organizational units
    if (![UserRole.HR_ADMIN, UserRole.CEO, UserRole.VP, UserRole.DIRECTOR].includes(userRole as UserRole)) {
      throw new ForbiddenException('You do not have permission to create organizational units');
    }

    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Calculate level based on parent
      let level = 0;
      if (createDto.parentId) {
        const parent = await this.orgUnitRepository.findOne({ where: { id: createDto.parentId } });
        if (!parent) {
          throw new NotFoundException(`Parent organizational unit with ID ${createDto.parentId} not found`);
        }
        level = parent.level + 1;
      }

      // Create the organizational unit
      const orgUnit = this.orgUnitRepository.create({
        name: createDto.name,
        type: createDto.type,
        description: createDto.description,
        parentId: createDto.parentId,
        level,
        managerId: createDto.managerId,
        budget: createDto.budget || 0,
      });

      const savedOrgUnit = await queryRunner.manager.save(orgUnit);
      await queryRunner.commitTransaction();

      // Security audit logging for NIS2 compliance
      // await this.loggingService.logEntry({
      //   level: LogLevel.INFO,
      //   source: LogSource.BACKEND,
      //   message: 'Organizational unit created',
      //   details: {
      //     unitId: savedOrgUnit.id,
      //     unitName: savedOrgUnit.name,
      //     unitType: savedOrgUnit.type,
      //     createdBy: userId,
      //     userRole: userRole,
      //     timestamp: new Date().toISOString()
      //   },
      //   component: 'organizational-units',
      //   action: 'create_unit',
      //   userId: userId.toString()
      // });

      return this.findOne(savedOrgUnit.id, userId, userRole);

    } catch (error) {
      await queryRunner.rollbackTransaction();

      // Log security audit for failed operations
      // await this.loggingService.logEntry({
      //   level: LogLevel.ERROR,
      //   source: LogSource.BACKEND,
      //   message: 'Failed to create organizational unit',
      //   details: {
      //     error: error.message,
      //     attemptedBy: userId,
      //     userRole: userRole,
      //     unitData: createDto,
      //     timestamp: new Date().toISOString()
      //   },
      //   component: 'organizational-units',
      //   action: 'create_unit_failed',
      //   userId: userId.toString()
      // });

      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async findAll(userId: number, userRole: string): Promise<OrganizationalUnit[]> {
    // HR_ADMIN and executives can see all organizational units
    if ([UserRole.HR_ADMIN, UserRole.CEO, UserRole.VP, UserRole.DIRECTOR].includes(userRole as UserRole)) {
      return this.orgUnitRepository.find({
        relations: ['manager', 'members', 'parent', 'children'],
        order: { level: 'ASC', name: 'ASC' },
      });
    }

    // Other users can only see their own organizational unit and its children
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['organizationalUnit'],
    });

    if (!user || !user.organizationalUnit) {
      return [];
    }

    return this.getUnitAndDescendants(user.organizationalUnit.id);
  }

  async findOne(id: number, userId: number, userRole: string): Promise<OrganizationalUnit> {
    const orgUnit = await this.orgUnitRepository.findOne({
      where: { id },
      relations: ['manager', 'members', 'parent', 'children'],
    });

    if (!orgUnit) {
      throw new NotFoundException(`Organizational unit with ID ${id} not found`);
    }

    // Check if user has permission to view this organizational unit
    if (![UserRole.HR_ADMIN, UserRole.CEO, UserRole.VP, UserRole.DIRECTOR].includes(userRole as UserRole)) {
      const user = await this.userRepository.findOne({
        where: { id: userId },
        relations: ['organizationalUnit'],
      });

      if (!user || !user.organizationalUnit || !this.isUserInUnitHierarchy(user.organizationalUnit.id, id)) {
        throw new ForbiddenException('You do not have permission to view this organizational unit');
      }
    }

    return orgUnit;
  }

  async update(id: number, updateDto: UpdateOrganizationalUnitDto, userId: number, userRole: string): Promise<OrganizationalUnit> {
    const orgUnit = await this.findOne(id, userId, userRole);

    // Check if user has permission to update this organizational unit
    if (![UserRole.HR_ADMIN, UserRole.CEO, UserRole.VP, UserRole.DIRECTOR].includes(userRole as UserRole)) {
      throw new ForbiddenException('You do not have permission to update organizational units');
    }

    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Update fields
      if (updateDto.name) orgUnit.name = updateDto.name;
      if (updateDto.type) orgUnit.type = updateDto.type;
      if (updateDto.description !== undefined) orgUnit.description = updateDto.description;
      if (updateDto.managerId !== undefined) orgUnit.managerId = updateDto.managerId;
      if (updateDto.budget !== undefined) orgUnit.budget = updateDto.budget;

      await queryRunner.manager.save(orgUnit);
      await queryRunner.commitTransaction();

      return this.findOne(orgUnit.id, userId, userRole);

    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async remove(id: number, userId: number, userRole: string): Promise<void> {
    const orgUnit = await this.findOne(id, userId, userRole);

    // Only HR_ADMIN and CEO can delete organizational units
    if (![UserRole.HR_ADMIN, UserRole.CEO].includes(userRole as UserRole)) {
      throw new ForbiddenException('Only HR admins and CEO can delete organizational units');
    }

    // Check if unit has children
    const children = await this.orgUnitRepository.find({ where: { parentId: id } });
    if (children.length > 0) {
      throw new ForbiddenException('Cannot delete organizational unit that has child units. Please reassign or delete child units first.');
    }

    // Reassign users to null organizational unit
    await this.userRepository.update(
      { organizationalUnitId: id },
      { organizationalUnitId: null }
    );

    // Remove the organizational unit
    await this.orgUnitRepository.remove(orgUnit);
  }

  async moveUnit(unitId: number, newParentId: number | null, userId: number, userRole: string): Promise<OrganizationalUnit> {
    // Only HR_ADMIN, CEO, VP, and DIRECTOR can move organizational units
    if (![UserRole.HR_ADMIN, UserRole.CEO, UserRole.VP, UserRole.DIRECTOR].includes(userRole as UserRole)) {
      throw new ForbiddenException('You do not have permission to move organizational units');
    }

    const unit = await this.findOne(unitId, userId, userRole);

    // Check for circular dependency
    if (newParentId && await this.wouldCreateCircularDependency(unitId, newParentId)) {
      throw new ForbiddenException('Cannot move unit: would create circular dependency');
    }

    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Calculate new level
      let newLevel = 0;
      if (newParentId) {
        const newParent = await this.orgUnitRepository.findOne({ where: { id: newParentId } });
        if (!newParent) {
          throw new NotFoundException(`Parent organizational unit with ID ${newParentId} not found`);
        }
        newLevel = newParent.level + 1;
      }

      // Update unit
      unit.parentId = newParentId;
      unit.level = newLevel;
      await queryRunner.manager.save(unit);

      // Update levels of all descendants
      await this.updateDescendantLevels(unitId, newLevel, queryRunner);

      await queryRunner.commitTransaction();

      return this.findOne(unitId, userId, userRole);

    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async getOrganizationalTree(): Promise<OrganizationalUnit[]> {
    // Get root units (no parent)
    const rootUnits = await this.orgUnitRepository.find({
      where: { parentId: null },
      relations: ['manager', 'members'],
      order: { name: 'ASC' },
    });

    // Recursively load children for each root unit
    for (const unit of rootUnits) {
      await this.loadChildren(unit);
    }

    return rootUnits;
  }

  private async loadChildren(unit: OrganizationalUnit): Promise<void> {
    unit.children = await this.orgUnitRepository.find({
      where: { parentId: unit.id },
      relations: ['manager', 'members'],
      order: { name: 'ASC' },
    });

    for (const child of unit.children) {
      await this.loadChildren(child);
    }
  }

  private async getUnitAndDescendants(unitId: number): Promise<OrganizationalUnit[]> {
    const units: OrganizationalUnit[] = [];
    const unit = await this.orgUnitRepository.findOne({
      where: { id: unitId },
      relations: ['manager', 'members', 'parent', 'children'],
    });

    if (unit) {
      units.push(unit);
      const children = await this.orgUnitRepository.find({ where: { parentId: unitId } });
      for (const child of children) {
        const descendants = await this.getUnitAndDescendants(child.id);
        units.push(...descendants);
      }
    }

    return units;
  }

  private async isUserInUnitHierarchy(userUnitId: number, targetUnitId: number): Promise<boolean> {
    if (userUnitId === targetUnitId) return true;

    const descendants = await this.getUnitAndDescendants(userUnitId);
    return descendants.some(unit => unit.id === targetUnitId);
  }

  private async wouldCreateCircularDependency(unitId: number, newParentId: number): Promise<boolean> {
    const descendants = await this.getUnitAndDescendants(unitId);
    return descendants.some(unit => unit.id === newParentId);
  }

  private async updateDescendantLevels(parentId: number, parentLevel: number, queryRunner: any): Promise<void> {
    const children = await this.orgUnitRepository.find({ where: { parentId } });

    for (const child of children) {
      child.level = parentLevel + 1;
      await queryRunner.manager.save(child);
      await this.updateDescendantLevels(child.id, child.level, queryRunner);
    }
  }
}
