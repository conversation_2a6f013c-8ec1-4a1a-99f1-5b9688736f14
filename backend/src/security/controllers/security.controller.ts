import { Controller, Post, Body, Get, UseGuards, Logger, Ip, Request } from '@nestjs/common';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../users/entities/user.entity';
import { SecurityHeadersService } from '../services/security-headers.service';
import { HttpsEnforcementService } from '../services/https-enforcement.service';
import { ProductionSecurityService } from '../services/production-security.service';

/**
 * 🔐 NIS2-Compliant Security Controller
 * 
 * Handles security-related endpoints:
 * - CSP violation reports
 * - Security configuration
 * - Security health checks
 * - Security audit endpoints
 */
@Controller('security')
export class SecurityController {
  private readonly logger = new Logger(SecurityController.name);

  constructor(
    private readonly securityHeadersService: SecurityHeadersService,
    private readonly httpsEnforcementService: HttpsEnforcementService,
    private readonly productionSecurityService: ProductionSecurityService,
  ) { }

  /**
   * 🔐 Content Security Policy violation report endpoint
   */
  @Post('csp-report')
  async handleCSPReport(@Body() report: any, @Ip() clientIp: string, @Request() req) {
    try {
      // 🔐 SECURITY: Sanitize CSP report input
      const sanitizedReport = this.sanitizeCSPReport(report['csp-report'] || report);

      const cspViolation = {
        timestamp: new Date().toISOString(),
        ip: clientIp,
        userAgent: this.sanitizeHeader(req.get('User-Agent')),
        referer: this.sanitizeHeader(req.get('Referer')),
        report: sanitizedReport,
      };

      // Log CSP violation for security analysis
      this.logger.warn('🔐 [CSP-VIOLATION] Content Security Policy violation detected:', cspViolation);

      // In production, send to SIEM/security monitoring system
      if (process.env.NODE_ENV === 'production') {
        await this.sendToSecurityMonitoring('csp_violation', cspViolation);
      }

      // Check for potential XSS attempts
      if (this.isPotentialXSSAttempt(cspViolation.report)) {
        this.logger.error('🔐 [SECURITY-ALERT] Potential XSS attempt detected via CSP violation:', cspViolation);
        await this.sendToSecurityMonitoring('potential_xss_attempt', cspViolation);
      }

      return { status: 'received' };
    } catch (error) {
      this.logger.error('🔐 [SECURITY] Error processing CSP report:', error);
      return { status: 'error' };
    }
  }

  /**
   * 🔐 Get security configuration (admin only) - Limited exposure
   */
  @Get('config')
  @UseGuards(JwtAuthGuard)
  @Roles(UserRole.CEO, UserRole.HR_ADMIN)
  getSecurityConfig() {
    // 🔐 SECURITY: Return limited security configuration without sensitive details
    const baseConfig = this.securityHeadersService.getSecurityConfig();

    return {
      // Only expose non-sensitive configuration
      security: {
        httpsEnabled: baseConfig.httpsEnabled || false,
        headersEnabled: true,
        cspEnabled: true,
        corsEnabled: true,
        rateLimitingEnabled: true,
        auditLoggingEnabled: true,
        mfaEnabled: true
      },
      environment: process.env.NODE_ENV === 'production' ? 'production' : 'development',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      compliance: {
        nis2: true,
        gdpr: true,
        iso27001: true
      }
    };
  }

  /**
   * 🔐 Security health check
   */
  @Get('health')
  getSecurityHealth() {
    const isProduction = process.env.NODE_ENV === 'production';
    const hasJWTSecret = !!process.env.JWT_SECRET;
    const jwtSecretLength = process.env.JWT_SECRET?.length || 0;

    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      security: {
        httpsEnforced: isProduction,
        jwtConfigured: hasJWTSecret,
        jwtSecretStrength: jwtSecretLength >= 32 ? 'strong' : 'weak',
        headersEnabled: true,
        cspEnabled: true,
        corsConfigured: true,
        rateLimitingEnabled: true,
      },
      compliance: {
        nis2: true,
        gdpr: true,
        iso27001: true,
      }
    };
  }

  /**
   * 🔐 Security metrics endpoint (admin only)
   */
  @Get('metrics')
  @UseGuards(JwtAuthGuard)
  @Roles(UserRole.CEO, UserRole.HR_ADMIN)
  getSecurityMetrics() {
    // In a real implementation, this would fetch from security monitoring system
    return {
      timestamp: new Date().toISOString(),
      period: '24h',
      metrics: {
        cspViolations: 0,
        failedLogins: 0,
        rateLimitHits: 0,
        suspiciousIPs: 0,
        mfaFailures: 0,
        passwordPolicyViolations: 0,
      },
      threats: {
        level: 'low',
        activeThreats: 0,
        blockedIPs: 0,
        quarantinedSessions: 0,
      },
      compliance: {
        auditLogRetention: '90 days',
        encryptionStatus: 'active',
        backupStatus: 'current',
        lastSecurityScan: new Date().toISOString(),
      }
    };
  }

  /**
   * 🔐 Report security incident (authenticated users)
   */
  @Post('incident-report')
  @UseGuards(JwtAuthGuard)
  async reportSecurityIncident(@Body() incident: any, @Request() req, @Ip() clientIp: string) {
    try {
      // 🔐 SECURITY: Sanitize incident report input
      const securityIncident = {
        timestamp: new Date().toISOString(),
        reportedBy: req.user.userId,
        reporterEmail: req.user.email,
        ip: clientIp,
        userAgent: this.sanitizeHeader(req.get('User-Agent')),
        incident: {
          type: this.sanitizeString(incident.type || ''),
          description: this.sanitizeString(incident.description || ''),
          severity: this.validateSeverity(incident.severity),
          affectedSystems: this.sanitizeArray(incident.affectedSystems),
          evidence: this.sanitizeEvidence(incident.evidence),
        }
      };

      this.logger.warn('🔐 [SECURITY-INCIDENT] User reported security incident:', securityIncident);

      // In production, trigger incident response workflow
      if (process.env.NODE_ENV === 'production') {
        await this.triggerIncidentResponse(securityIncident);
      }

      return {
        status: 'received',
        incidentId: this.generateIncidentId(),
        message: 'Security incident report received and will be investigated'
      };
    } catch (error) {
      this.logger.error('🔐 [SECURITY] Error processing incident report:', error);
      return { status: 'error', message: 'Failed to process incident report' };
    }
  }

  /**
   * 🔐 Sanitize CSP report input to prevent injection attacks
   */
  private sanitizeCSPReport(report: any): any {
    if (!report || typeof report !== 'object') {
      return {};
    }

    const sanitized: any = {};
    const allowedFields = [
      'document-uri', 'referrer', 'violated-directive', 'effective-directive',
      'original-policy', 'disposition', 'blocked-uri', 'line-number',
      'column-number', 'source-file', 'status-code', 'script-sample'
    ];

    for (const field of allowedFields) {
      if (report[field] !== undefined) {
        sanitized[field] = this.sanitizeString(String(report[field]));
      }
    }

    return sanitized;
  }

  /**
   * 🔐 Sanitize header values
   */
  private sanitizeHeader(header: string | undefined): string {
    if (!header) return '';
    return this.sanitizeString(header);
  }

  /**
   * 🔐 Sanitize string input
   */
  private sanitizeString(input: string): string {
    if (!input) return '';

    // Limit length to prevent buffer overflow
    let sanitized = input.substring(0, 1000);

    // Remove null bytes and control characters
    sanitized = sanitized.replace(/[\x00-\x1F\x7F]/g, '');

    // Escape HTML entities to prevent XSS
    sanitized = sanitized
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;');

    return sanitized;
  }

  /**
   * 🔐 Validate and sanitize severity level
   */
  private validateSeverity(severity: string): string {
    const validSeverities = ['low', 'medium', 'high', 'critical'];
    const sanitized = this.sanitizeString(severity || 'medium').toLowerCase();
    return validSeverities.includes(sanitized) ? sanitized : 'medium';
  }

  /**
   * 🔐 Sanitize array input
   */
  private sanitizeArray(input: any): string[] {
    if (!Array.isArray(input)) return [];
    return input
      .slice(0, 10) // Limit array size
      .map(item => this.sanitizeString(String(item)))
      .filter(item => item.length > 0);
  }

  /**
   * 🔐 Sanitize evidence object
   */
  private sanitizeEvidence(evidence: any): any {
    if (!evidence || typeof evidence !== 'object') return {};

    const sanitized: any = {};
    const allowedFields = ['url', 'timestamp', 'userAgent', 'screenshot', 'logs', 'description'];

    for (const field of allowedFields) {
      if (evidence[field] !== undefined) {
        if (typeof evidence[field] === 'string') {
          sanitized[field] = this.sanitizeString(evidence[field]);
        } else if (Array.isArray(evidence[field])) {
          sanitized[field] = this.sanitizeArray(evidence[field]);
        }
      }
    }

    return sanitized;
  }

  /**
   * 🔐 Check if CSP violation indicates potential XSS attempt
   */
  private isPotentialXSSAttempt(report: any): boolean {
    if (!report) return false;

    const suspiciousPatterns = [
      'javascript:',
      'data:text/html',
      'vbscript:',
      'onload=',
      'onerror=',
      'onclick=',
      '<script',
      'eval(',
      'setTimeout(',
      'setInterval(',
    ];

    const reportString = JSON.stringify(report).toLowerCase();
    return suspiciousPatterns.some(pattern => reportString.includes(pattern));
  }

  /**
   * 🔐 Send security event to monitoring system
   */
  private async sendToSecurityMonitoring(eventType: string, data: any): Promise<void> {
    // In production, this would integrate with SIEM/security monitoring
    this.logger.log(`🔐 [SECURITY-MONITORING] ${eventType}:`, data);
  }

  /**
   * 🔐 Trigger incident response workflow
   */
  private async triggerIncidentResponse(incident: any): Promise<void> {
    // In production, this would trigger automated incident response
    this.logger.log('🔐 [INCIDENT-RESPONSE] Triggered for incident:', incident);
  }

  /**
   * 🔐 Get HTTPS configuration status (admin only)
   */
  @Get('https-status')
  @UseGuards(JwtAuthGuard)
  @Roles(UserRole.CEO, UserRole.HR_ADMIN)
  async getHttpsStatus() {
    try {
      const httpsConfig = this.httpsEnforcementService.getHttpsConfiguration();
      const sslInfo = await this.httpsEnforcementService.getSSLCertificateInfo();
      const validation = await this.httpsEnforcementService.validateHttpsConfiguration();

      return {
        status: 'ok',
        timestamp: new Date().toISOString(),
        https: httpsConfig,
        ssl: sslInfo,
        validation,
      };
    } catch (error) {
      this.logger.error('🔐 [SECURITY] Error getting HTTPS status:', error);
      return { status: 'error', message: 'Failed to get HTTPS status' };
    }
  }

  /**
   * 🔐 Get production security validation (admin only)
   */
  @Get('production-validation')
  @UseGuards(JwtAuthGuard)
  @Roles(UserRole.CEO, UserRole.HR_ADMIN)
  async getProductionValidation() {
    try {
      const validation = await this.productionSecurityService.runSecurityValidation();
      const healthStatus = await this.productionSecurityService.getSecurityHealthStatus();

      return {
        status: 'ok',
        timestamp: new Date().toISOString(),
        validation,
        health: healthStatus,
      };
    } catch (error) {
      this.logger.error('🔐 [SECURITY] Error getting production validation:', error);
      return { status: 'error', message: 'Failed to get production validation' };
    }
  }

  /**
   * 🔐 Get comprehensive security status (admin only)
   */
  @Get('comprehensive-status')
  @UseGuards(JwtAuthGuard)
  @Roles(UserRole.CEO, UserRole.HR_ADMIN)
  async getComprehensiveSecurityStatus() {
    try {
      const [
        securityConfig,
        httpsStatus,
        productionValidation,
        healthStatus
      ] = await Promise.all([
        this.securityHeadersService.getSecurityConfig(),
        this.httpsEnforcementService.validateHttpsConfiguration(),
        this.productionSecurityService.runSecurityValidation(),
        this.productionSecurityService.getSecurityHealthStatus()
      ]);

      return {
        status: 'ok',
        timestamp: new Date().toISOString(),
        overall: {
          securityScore: productionValidation.securityScore,
          status: productionValidation.status,
          compliance: productionValidation.compliance,
        },
        components: {
          headers: {
            status: 'active',
            config: securityConfig,
          },
          https: {
            status: httpsStatus.status,
            validation: httpsStatus,
          },
          production: {
            status: productionValidation.status,
            score: productionValidation.securityScore,
            issues: productionValidation.criticalIssues.length,
            warnings: productionValidation.warnings.length,
          },
          health: healthStatus,
        },
        recommendations: [
          ...productionValidation.recommendations.slice(0, 3),
          ...httpsStatus.recommendations.slice(0, 2),
        ],
      };
    } catch (error) {
      this.logger.error('🔐 [SECURITY] Error getting comprehensive security status:', error);
      return { status: 'error', message: 'Failed to get comprehensive security status' };
    }
  }

  /**
   * 🔐 Generate unique incident ID
   */
  private generateIncidentId(): string {
    const crypto = require('crypto');
    const timestamp = Date.now().toString(36);
    const random = crypto.randomBytes(4).toString('hex');
    return `INC-${timestamp}-${random}`.toUpperCase();
  }
}
