import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, Request } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AnalyticsService } from './analytics.service';
import { CreateDashboardDto, UpdateDashboardDto } from './dto/dashboard.dto';
import { CreateSurveyDto, UpdateSurveyDto } from './dto/survey.dto';

@Controller('analytics')
export class AnalyticsController {
  constructor(private readonly analyticsService: AnalyticsService) { }

  // 🔐 NIS2-COMPLIANT: Secured dashboard endpoints (auth required)
  @Get('metrics/summary')
  @UseGuards(JwtAuthGuard)
  async getMetricsSummary(@Request() req) {
    return this.analyticsService.getMetricsSummary(req.user.userId, req.user.role);
  }

  @Get('engagement/summary')
  @UseGuards(JwtAuthGuard)
  async getEngagementSummary(@Request() req) {
    return this.analyticsService.getEngagementSummary(req.user.userId, req.user.role);
  }

  // Protected endpoints (auth required)
  // Dashboard endpoints
  @Get('dashboards')
  @UseGuards(JwtAuthGuard)
  async getDashboards(@Request() req) {
    return this.analyticsService.getUserDashboards(req.user.id);
  }

  @Get('dashboards/:id')
  @UseGuards(JwtAuthGuard)
  async getDashboard(@Param('id') id: number, @Request() req) {
    return this.analyticsService.getDashboard(id, req.user.id);
  }

  @Post('dashboards')
  @UseGuards(JwtAuthGuard)
  async createDashboard(@Body() createDashboardDto: CreateDashboardDto, @Request() req) {
    return this.analyticsService.createDashboard(createDashboardDto, req.user.id);
  }

  @Put('dashboards/:id')
  @UseGuards(JwtAuthGuard)
  async updateDashboard(
    @Param('id') id: number,
    @Body() updateDashboardDto: UpdateDashboardDto,
    @Request() req
  ) {
    return this.analyticsService.updateDashboard(id, updateDashboardDto, req.user.id);
  }

  @Delete('dashboards/:id')
  @UseGuards(JwtAuthGuard)
  async deleteDashboard(@Param('id') id: number, @Request() req) {
    return this.analyticsService.deleteDashboard(id, req.user.id);
  }

  // Performance metrics endpoints
  @Get('metrics')
  @UseGuards(JwtAuthGuard)
  async getMetrics(
    @Query('type') type?: string,
    @Query('userId') userId?: number,
    @Query('orgUnitId') orgUnitId?: number,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string
  ) {
    return this.analyticsService.getPerformanceMetrics({
      type,
      userId,
      orgUnitId,
      startDate,
      endDate,
    });
  }



  // Survey endpoints (public)
  @Get('surveys')
  async getSurveys(@Query('status') status?: string) {
    return this.analyticsService.getSurveys(status);
  }

  @Get('surveys/:id')
  async getSurvey(@Param('id') id: number) {
    return this.analyticsService.getSurvey(id);
  }

  // Survey endpoints (protected)
  @Post('surveys')
  @UseGuards(JwtAuthGuard)
  async createSurvey(@Body() createSurveyDto: CreateSurveyDto, @Request() req) {
    return this.analyticsService.createSurvey(createSurveyDto, req.user.id);
  }

  @Put('surveys/:id')
  @UseGuards(JwtAuthGuard)
  async updateSurvey(
    @Param('id') id: number,
    @Body() updateSurveyDto: UpdateSurveyDto,
    @Request() req
  ) {
    return this.analyticsService.updateSurvey(id, updateSurveyDto, req.user.id);
  }

  @Post('surveys/:id/responses')
  @UseGuards(JwtAuthGuard)
  async submitSurveyResponse(
    @Param('id') surveyId: number,
    @Body() responseData: any,
    @Request() req
  ) {
    return this.analyticsService.submitSurveyResponse(surveyId, responseData, req.user.id);
  }

  @Get('surveys/:id/responses')
  @UseGuards(JwtAuthGuard)
  async getSurveyResponses(@Param('id') surveyId: number) {
    return this.analyticsService.getSurveyResponses(surveyId);
  }

  @Get('surveys/:id/analytics')
  @UseGuards(JwtAuthGuard)
  async getSurveyAnalytics(@Param('id') surveyId: number) {
    return this.analyticsService.getSurveyAnalytics(surveyId);
  }

  // Engagement analytics (protected)
  @Get('engagement/trends')
  @UseGuards(JwtAuthGuard)
  async getEngagementTrends(
    @Query('period') period: string = '3months',
    @Query('orgUnitId') orgUnitId?: number
  ) {
    return this.analyticsService.getEngagementTrends(period, orgUnitId);
  }

  // 🔐 NIS2-COMPLIANT: Enhanced analytics endpoints with real data
  @Get('attrition/risk-analysis')
  @UseGuards(JwtAuthGuard)
  async getAttritionRiskAnalysis(
    @Query('teamId') teamId?: number,
    @Query('role') role?: string,
    @Query('tenure') tenure?: string,
    @Query('departmentId') departmentId?: number
  ) {
    return this.analyticsService.getAttritionRiskAnalysis({
      teamId,
      role,
      tenure,
      departmentId
    });
  }

  @Get('performance/analytics')
  @UseGuards(JwtAuthGuard)
  async getPerformanceAnalytics(
    @Query('teamId') teamId?: number,
    @Query('role') role?: string,
    @Query('departmentId') departmentId?: number,
    @Query('period') period?: string
  ) {
    return this.analyticsService.getPerformanceAnalytics({
      teamId,
      role,
      departmentId,
      period
    });
  }

  // 🔐 NIS2-COMPLIANT: Team performance endpoint for real data
  @Get('team/performance')
  @UseGuards(JwtAuthGuard)
  async getTeamPerformance(
    @Query('teamId') teamId?: number,
    @Query('period') period?: string
  ) {
    return this.analyticsService.getTeamPerformance({
      teamId,
      period
    });
  }

  @Get('teams/performance-comparison')
  @UseGuards(JwtAuthGuard)
  async getTeamPerformanceComparison(
    @Query('period') period?: string,
    @Query('includeInactive') includeInactive?: boolean
  ) {
    return this.analyticsService.getTeamPerformanceComparison({
      period,
      includeInactive
    });
  }

}
