import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, In } from 'typeorm';
import { AnalyticsDashboard } from './entities/analytics-dashboard.entity';
import { PerformanceMetric } from './entities/performance-metric.entity';
import { EngagementSurvey } from './entities/engagement-survey.entity';
import { SurveyResponse } from './entities/survey-response.entity';
import { User } from '../users/entities/user.entity';
import { AssessmentInstance } from '../assessments/entities/assessment-instance.entity';
import { CreateDashboardDto, UpdateDashboardDto } from './dto/dashboard.dto';
import { CreateSurveyDto, UpdateSurveyDto } from './dto/survey.dto';

@Injectable()
export class AnalyticsService {
  constructor(
    @InjectRepository(AnalyticsDashboard)
    private dashboardRepository: Repository<AnalyticsDashboard>,
    @InjectRepository(PerformanceMetric)
    private metricsRepository: Repository<PerformanceMetric>,
    @InjectRepository(EngagementSurvey)
    private surveyRepository: Repository<EngagementSurvey>,
    @InjectRepository(SurveyResponse)
    private responseRepository: Repository<SurveyResponse>,
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    @InjectRepository(AssessmentInstance)
    private assessmentRepository: Repository<AssessmentInstance>,
  ) { }

  // Public dashboard summary methods
  async getMetricsSummary(userId?: number, userRole?: string) {
    try {
      // Get basic metrics from the database
      const totalMetrics = await this.metricsRepository.count();
      const recentMetrics = await this.metricsRepository.find({
        order: { id: 'DESC' },
        take: 10
      });

      // 🔐 NIS2-COMPLIANT: Calculate real metrics from database
      const assessments = await this.assessmentRepository.find({
        where: { status: In(['COMPLETED', 'APPROVED']) },
        order: { assessmentDate: 'DESC' },
        take: 100
      });

      const averageScore = assessments.length > 0
        ? assessments.reduce((sum, a) => sum + (a.totalScore || 0), 0) / assessments.length
        : 0;

      // Calculate improvement trend (compare last 30 days vs previous 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      const sixtyDaysAgo = new Date();
      sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60);

      const recentAssessments = assessments.filter(a => new Date(a.assessmentDate) >= thirtyDaysAgo);
      const previousAssessments = assessments.filter(a =>
        new Date(a.assessmentDate) >= sixtyDaysAgo && new Date(a.assessmentDate) < thirtyDaysAgo
      );

      const recentAvg = recentAssessments.length > 0
        ? recentAssessments.reduce((sum, a) => sum + (a.totalScore || 0), 0) / recentAssessments.length
        : 0;
      const previousAvg = previousAssessments.length > 0
        ? previousAssessments.reduce((sum, a) => sum + (a.totalScore || 0), 0) / previousAssessments.length
        : 0;

      const improvementTrend = previousAvg > 0 ? ((recentAvg - previousAvg) / previousAvg) * 100 : 0;

      return {
        success: true,
        data: {
          averageScore: Math.round(averageScore * 10) / 10,
          improvementTrend: Math.round(improvementTrend * 10) / 10,
          totalMetrics,
          recentCount: recentMetrics.length
        }
      };
    } catch (error) {
      console.error('Error fetching metrics summary:', error);
      return {
        success: true,
        data: {
          averageScore: 0,
          improvementTrend: 0,
          totalMetrics: 0,
          recentCount: 0
        }
      };
    }
  }

  async getEngagementSummary(userId?: number, userRole?: string) {
    try {
      // Get recent activity from surveys and responses
      const recentSurveys = await this.surveyRepository.find({
        order: { id: 'DESC' },
        take: 4,
        relations: ['responses']
      });

      const activities = recentSurveys.map((survey, index) => ({
        id: survey.id,
        type: 'survey',
        title: survey.title || `Survey ${survey.id}`,
        description: survey.description || 'Employee engagement survey',
        timestamp: survey.createdAt,
        status: 'active' // Default status since isActive property doesn't exist
      }));

      return {
        success: true,
        data: activities
      };
    } catch (error) {
      console.error('Error fetching engagement summary:', error);
      return {
        success: true,
        data: []
      };
    }
  }

  // Dashboard methods
  async getUserDashboards(userId: number) {
    const dashboards = await this.dashboardRepository.find({
      where: [
        { userId },
        { isShared: true }
      ],
      order: { isDefault: 'DESC', createdAt: 'DESC' }
    });

    return {
      success: true,
      data: dashboards
    };
  }

  async getDashboard(id: number, userId: number) {
    const dashboard = await this.dashboardRepository.findOne({
      where: { id },
      relations: ['user']
    });

    if (!dashboard) {
      throw new NotFoundException('Dashboard not found');
    }

    if (dashboard.userId !== userId && !dashboard.isShared) {
      throw new ForbiddenException('Access denied to this dashboard');
    }

    return {
      success: true,
      data: dashboard
    };
  }

  async createDashboard(createDashboardDto: CreateDashboardDto, userId: number) {
    const dashboard = this.dashboardRepository.create({
      ...createDashboardDto,
      userId
    });

    const savedDashboard = await this.dashboardRepository.save(dashboard);

    return {
      success: true,
      data: savedDashboard
    };
  }

  async updateDashboard(id: number, updateDashboardDto: UpdateDashboardDto, userId: number) {
    const dashboard = await this.dashboardRepository.findOne({ where: { id } });

    if (!dashboard) {
      throw new NotFoundException('Dashboard not found');
    }

    if (dashboard.userId !== userId) {
      throw new ForbiddenException('Access denied to this dashboard');
    }

    await this.dashboardRepository.update(id, updateDashboardDto);
    const updatedDashboard = await this.dashboardRepository.findOne({ where: { id } });

    return {
      success: true,
      data: updatedDashboard
    };
  }

  async deleteDashboard(id: number, userId: number) {
    const dashboard = await this.dashboardRepository.findOne({ where: { id } });

    if (!dashboard) {
      throw new NotFoundException('Dashboard not found');
    }

    if (dashboard.userId !== userId) {
      throw new ForbiddenException('Access denied to this dashboard');
    }

    await this.dashboardRepository.delete(id);

    return {
      success: true,
      message: 'Dashboard deleted successfully'
    };
  }

  // Performance metrics methods
  async getPerformanceMetrics(filters: any) {
    const queryBuilder = this.metricsRepository.createQueryBuilder('metric')
      .leftJoinAndSelect('metric.user', 'user')
      .leftJoinAndSelect('metric.organizationalUnit', 'orgUnit');

    if (filters.type) {
      queryBuilder.andWhere('metric.metricType = :type', { type: filters.type });
    }

    if (filters.userId) {
      queryBuilder.andWhere('metric.userId = :userId', { userId: filters.userId });
    }

    if (filters.orgUnitId) {
      queryBuilder.andWhere('metric.organizationalUnitId = :orgUnitId', { orgUnitId: filters.orgUnitId });
    }

    if (filters.startDate && filters.endDate) {
      queryBuilder.andWhere('metric.periodStart >= :startDate AND metric.periodEnd <= :endDate', {
        startDate: filters.startDate,
        endDate: filters.endDate
      });
    }

    queryBuilder.orderBy('metric.calculationDate', 'DESC');

    const metrics = await queryBuilder.getMany();

    return {
      success: true,
      data: metrics
    };
  }



  // Survey methods
  async getSurveys(status?: string) {
    const queryBuilder = this.surveyRepository.createQueryBuilder('survey')
      .leftJoinAndSelect('survey.createdBy', 'creator')
      .leftJoinAndSelect('survey.responses', 'responses');

    if (status) {
      queryBuilder.where('survey.status = :status', { status });
    }

    queryBuilder.orderBy('survey.createdAt', 'DESC');

    const surveys = await queryBuilder.getMany();

    return {
      success: true,
      data: surveys
    };
  }

  async getSurvey(id: number) {
    const survey = await this.surveyRepository.findOne({
      where: { id },
      relations: ['createdBy', 'responses', 'responses.respondent']
    });

    if (!survey) {
      throw new NotFoundException('Survey not found');
    }

    return {
      success: true,
      data: survey
    };
  }

  async createSurvey(createSurveyDto: CreateSurveyDto, userId: number) {
    const survey = this.surveyRepository.create({
      ...createSurveyDto,
      createdById: userId
    });

    const savedSurvey = await this.surveyRepository.save(survey);

    return {
      success: true,
      data: savedSurvey
    };
  }

  async updateSurvey(id: number, updateSurveyDto: UpdateSurveyDto, userId: number) {
    const survey = await this.surveyRepository.findOne({ where: { id } });

    if (!survey) {
      throw new NotFoundException('Survey not found');
    }

    if (survey.createdById !== userId) {
      throw new ForbiddenException('Access denied to this survey');
    }

    await this.surveyRepository.update(id, updateSurveyDto);
    const updatedSurvey = await this.surveyRepository.findOne({ where: { id } });

    return {
      success: true,
      data: updatedSurvey
    };
  }

  async submitSurveyResponse(surveyId: number, responseData: any, userId: number) {
    const survey = await this.surveyRepository.findOne({ where: { id: surveyId } });

    if (!survey) {
      throw new NotFoundException('Survey not found');
    }

    const response = this.responseRepository.create({
      surveyId,
      respondentId: userId,
      responses: responseData.responses,
      completionTime: responseData.completionTime || null
    });

    const savedResponse = await this.responseRepository.save(response);

    return {
      success: true,
      data: savedResponse
    };
  }

  async getSurveyResponses(surveyId: number) {
    const responses = await this.responseRepository.find({
      where: { surveyId },
      relations: ['respondent'],
      order: { submittedAt: 'DESC' }
    });

    return {
      success: true,
      data: responses
    };
  }

  async getSurveyAnalytics(surveyId: number) {
    const survey = await this.surveyRepository.findOne({
      where: { id: surveyId },
      relations: ['responses']
    });

    if (!survey) {
      throw new NotFoundException('Survey not found');
    }

    const totalResponses = survey.responses.length;
    const avgCompletionTime = survey.responses.reduce((sum, r) => sum + (r.completionTime || 0), 0) / totalResponses;

    // Basic analytics - can be expanded
    const analytics = {
      totalResponses,
      avgCompletionTime: Math.round(avgCompletionTime),
      responseRate: totalResponses, // Would need target audience size for actual rate
      responses: survey.responses
    };

    return {
      success: true,
      data: analytics
    };
  }

  async getEngagementTrends(period: string, orgUnitId?: number) {
    try {
      // Calculate date range based on period
      const endDate = new Date();
      const startDate = new Date();

      switch (period) {
        case '1month':
          startDate.setMonth(endDate.getMonth() - 1);
          break;
        case '3months':
          startDate.setMonth(endDate.getMonth() - 3);
          break;
        case '6months':
          startDate.setMonth(endDate.getMonth() - 6);
          break;
        case '1year':
          startDate.setFullYear(endDate.getFullYear() - 1);
          break;
        default:
          startDate.setMonth(endDate.getMonth() - 3);
      }

      // Get engagement metrics for the period
      const metrics = await this.metricsRepository.find({
        where: {
          metricName: 'engagement_score',
          ...(orgUnitId && { organizationalUnitId: orgUnitId })
        },
        order: { calculationDate: 'ASC' }
      });

      // Process data for trends
      const chartData = metrics.map(metric => ({
        period: metric.calculationDate.toISOString().split('T')[0],
        score: Number(metric.metricValue),
        date: metric.calculationDate.toISOString()
      }));

      const currentScore = chartData.length > 0 ? chartData[chartData.length - 1].score : 0;
      const previousScore = chartData.length > 1 ? chartData[chartData.length - 2].score : currentScore;
      const trendPercentage = previousScore > 0 ? ((currentScore - previousScore) / previousScore) * 100 : 0;

      return {
        success: true,
        data: {
          currentScore,
          previousScore,
          trend: trendPercentage > 0 ? 'up' : trendPercentage < 0 ? 'down' : 'stable',
          trendPercentage: Math.abs(trendPercentage),
          chartData
        }
      };
    } catch (error) {
      console.error('Error fetching engagement trends:', error);
      return {
        success: false,
        error: 'Failed to fetch engagement trends'
      };
    }
  }

  // 🔐 NIS2-COMPLIANT: Enhanced analytics with real data integration using centralized API
  async getAttritionRiskAnalysis(filters?: {
    teamId?: number;
    role?: string;
    tenure?: string;
    departmentId?: number;
  }) {
    try {
      // Use centralized database API instead of raw SQL
      const usersQuery = this.usersRepository.createQueryBuilder('user')
        .leftJoinAndSelect('user.organizationalUnit', 'orgUnit')
        .where('user.isActive = :isActive', { isActive: true });

      // Apply filters using QueryBuilder instead of raw SQL
      if (filters?.teamId) {
        usersQuery.andWhere('user.teamId = :teamId', { teamId: filters.teamId });
      }
      if (filters?.role) {
        usersQuery.andWhere('user.role = :role', { role: filters.role });
      }
      if (filters?.departmentId) {
        usersQuery.andWhere('orgUnit.id = :departmentId', { departmentId: filters.departmentId });
      }

      const users = await usersQuery.getMany();

      // Get assessment data for each user using proper repository methods
      const employees = await Promise.all(users.map(async (user) => {
        const assessments = await this.assessmentRepository.find({
          where: {
            employeeId: user.id,
            status: In(['COMPLETED', 'APPROVED'])
          },
          order: { assessmentDate: 'DESC' }
        });

        const avgPerformance = assessments.length > 0
          ? assessments.reduce((sum, a) => sum + (a.totalScore || 0), 0) / assessments.length
          : 0;

        const tenureDays = Math.floor((Date.now() - new Date(user.createdAt).getTime()) / (1000 * 60 * 60 * 24));

        return {
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          role: user.role,
          hireDate: user.createdAt,
          department: user.organizationalUnit?.name || 'Unassigned',
          avgPerformance,
          assessmentCount: assessments.length,
          lastAssessmentDate: assessments[0]?.assessmentDate || null,
          tenureDays
        };
      }));

      // Calculate attrition risk scores using multiple factors
      const riskAnalysis = employees.map(emp => {
        let riskScore = 0;
        const factors = [];

        // Performance factor (30% weight)
        const performanceScore = emp.avgPerformance || 0;
        if (performanceScore < 60) {
          riskScore += 30;
          factors.push('Low performance scores');
        } else if (performanceScore < 75) {
          riskScore += 15;
          factors.push('Below average performance');
        }

        // Tenure factor (25% weight)
        const tenureMonths = emp.tenureDays / 30;
        if (tenureMonths < 6) {
          riskScore += 25;
          factors.push('New employee (< 6 months)');
        } else if (tenureMonths > 36) {
          riskScore += 10;
          factors.push('Long tenure (potential stagnation)');
        }

        // Assessment engagement factor (20% weight)
        if (emp.assessmentCount === 0) {
          riskScore += 20;
          factors.push('No performance assessments');
        } else if (emp.lastAssessmentDate) {
          const daysSinceLastAssessment = Math.floor(
            (new Date().getTime() - new Date(emp.lastAssessmentDate).getTime()) / (1000 * 60 * 60 * 24)
          );
          if (daysSinceLastAssessment > 180) {
            riskScore += 15;
            factors.push('No recent assessments');
          }
        }

        // Role-based factor (15% weight)
        if (['EMPLOYEE', 'INTERN'].includes(emp.role)) {
          riskScore += 10;
          factors.push('Entry-level role');
        }

        // Department factor (10% weight)
        if (!emp.department) {
          riskScore += 10;
          factors.push('No assigned department');
        }

        // Determine risk level
        let riskLevel: 'low' | 'medium' | 'high' | 'critical';
        if (riskScore >= 70) riskLevel = 'critical';
        else if (riskScore >= 50) riskLevel = 'high';
        else if (riskScore >= 30) riskLevel = 'medium';
        else riskLevel = 'low';

        return {
          employeeId: emp.id,
          name: `${emp.firstName} ${emp.lastName}`,
          role: emp.role,
          department: emp.department,
          riskScore,
          riskLevel,
          factors,
          tenure: Math.floor(tenureMonths),
          lastPerformanceScore: performanceScore
        };
      });

      // Calculate overall statistics
      const totalEmployees = riskAnalysis.length;
      const riskDistribution = {
        low: riskAnalysis.filter(r => r.riskLevel === 'low').length,
        medium: riskAnalysis.filter(r => r.riskLevel === 'medium').length,
        high: riskAnalysis.filter(r => r.riskLevel === 'high').length,
        critical: riskAnalysis.filter(r => r.riskLevel === 'critical').length,
      };

      const atRiskCount = riskDistribution.high + riskDistribution.critical;
      const overallRiskScore = totalEmployees > 0 ?
        riskAnalysis.reduce((sum, r) => sum + r.riskScore, 0) / totalEmployees : 0;

      let overallRisk: 'low' | 'medium' | 'high' | 'critical';
      if (overallRiskScore >= 70) overallRisk = 'critical';
      else if (overallRiskScore >= 50) overallRisk = 'high';
      else if (overallRiskScore >= 30) overallRisk = 'medium';
      else overallRisk = 'low';

      // Generate recommendations
      const recommendations = [];
      if (riskDistribution.critical > 0) {
        recommendations.push('Immediate intervention needed for critical risk employees');
      }
      if (riskDistribution.high > totalEmployees * 0.2) {
        recommendations.push('High attrition risk detected - review retention strategies');
      }
      if (riskAnalysis.filter(r => r.factors.includes('Low performance scores')).length > 0) {
        recommendations.push('Focus on performance improvement programs');
      }
      if (riskAnalysis.filter(r => r.factors.includes('New employee (< 6 months)')).length > 0) {
        recommendations.push('Strengthen onboarding and early engagement programs');
      }

      return {
        success: true,
        data: {
          overallRisk,
          riskScore: Math.round(overallRiskScore),
          totalEmployees,
          atRiskCount,
          riskDistribution,
          topRiskFactors: this.getTopRiskFactors(riskAnalysis),
          recommendations,
          employeeDetails: riskAnalysis.sort((a, b) => b.riskScore - a.riskScore).slice(0, 10)
        }
      };
    } catch (error) {
      console.error('Error calculating attrition risk:', error);
      return {
        success: false,
        error: 'Failed to calculate attrition risk'
      };
    }
  }

  private getTopRiskFactors(riskAnalysis: any[]): string[] {
    const factorCounts = {};
    riskAnalysis.forEach(emp => {
      emp.factors.forEach(factor => {
        factorCounts[factor] = (factorCounts[factor] || 0) + 1;
      });
    });

    return Object.entries(factorCounts)
      .sort(([, a], [, b]) => (b as number) - (a as number))
      .slice(0, 5)
      .map(([factor]) => factor);
  }

  // 🔐 NIS2-COMPLIANT: Performance analytics with drill-down capabilities
  async getPerformanceAnalytics(filters?: {
    teamId?: number;
    role?: string;
    departmentId?: number;
    period?: string;
  }) {
    try {
      const endDate = new Date();
      const startDate = new Date();

      // Set date range based on period
      switch (filters?.period) {
        case '1month':
          startDate.setMonth(endDate.getMonth() - 1);
          break;
        case '6months':
          startDate.setMonth(endDate.getMonth() - 6);
          break;
        case '1year':
          startDate.setFullYear(endDate.getFullYear() - 1);
          break;
        default:
          startDate.setMonth(endDate.getMonth() - 3);
      }

      // Use centralized database API instead of raw SQL
      const usersQuery = this.usersRepository.createQueryBuilder('user')
        .leftJoinAndSelect('user.organizationalUnit', 'orgUnit')
        .where('user.isActive = :isActive', { isActive: true });

      // Apply filters using QueryBuilder
      if (filters?.teamId) {
        usersQuery.andWhere('user.teamId = :teamId', { teamId: filters.teamId });
      }
      if (filters?.role) {
        usersQuery.andWhere('user.role = :role', { role: filters.role });
      }
      if (filters?.departmentId) {
        usersQuery.andWhere('orgUnit.id = :departmentId', { departmentId: filters.departmentId });
      }

      const users = await usersQuery.getMany();

      // Get assessment data using proper repository methods
      const results = await Promise.all(users.map(async (user) => {
        const assessments = await this.assessmentRepository.find({
          where: {
            employeeId: user.id,
            status: In(['COMPLETED', 'APPROVED']),
            assessmentDate: Between(startDate, endDate)
          },
          relations: ['template'],
          order: { assessmentDate: 'DESC' }
        });

        return assessments.map((assessment, index) => ({
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          department: user.organizationalUnit?.name || 'Unassigned',
          totalScore: assessment.totalScore,
          assessmentDate: assessment.assessmentDate,
          status: assessment.status,
          templateName: assessment.template?.name || 'Unknown',
          assessmentCount: assessments.length,
          avgScore: assessments.reduce((sum, a) => sum + (a.totalScore || 0), 0) / assessments.length,
          rn: index + 1
        }));
      })).then(results => results.flat());

      // Process performance data
      const employeePerformance = {};
      results.forEach(row => {
        if (!employeePerformance[row.id]) {
          employeePerformance[row.id] = {
            id: row.id,
            name: `${row.firstName} ${row.lastName}`,
            role: row.role,
            department: row.department,
            assessments: [],
            avgScore: row.avgScore || 0,
            assessmentCount: row.assessmentCount || 0
          };
        }

        if (row.totalScore) {
          employeePerformance[row.id].assessments.push({
            score: row.totalScore,
            date: row.assessmentDate,
            template: row.templateName,
            status: row.status
          });
        }
      });

      const employees = Object.values(employeePerformance) as any[];

      // Calculate performance distribution
      const performanceDistribution = {
        excellent: employees.filter(e => e.avgScore >= 90).length,
        good: employees.filter(e => e.avgScore >= 75 && e.avgScore < 90).length,
        satisfactory: employees.filter(e => e.avgScore >= 60 && e.avgScore < 75).length,
        needsImprovement: employees.filter(e => e.avgScore < 60).length,
      };

      // Calculate team/department averages
      const departmentStats = {};
      employees.forEach(emp => {
        if (emp.department) {
          if (!departmentStats[emp.department]) {
            departmentStats[emp.department] = { scores: [], count: 0 };
          }
          if (emp.avgScore > 0) {
            departmentStats[emp.department].scores.push(emp.avgScore);
            departmentStats[emp.department].count++;
          }
        }
      });

      const departmentAverages = Object.entries(departmentStats).map(([dept, stats]: [string, any]) => ({
        department: dept,
        averageScore: stats.scores.length > 0 ?
          stats.scores.reduce((sum: number, score: number) => sum + score, 0) / stats.scores.length : 0,
        employeeCount: stats.count
      })).sort((a, b) => b.averageScore - a.averageScore);

      // Calculate overall metrics
      const totalEmployees = employees.length;
      const employeesWithAssessments = employees.filter(e => e.assessmentCount > 0).length;
      const overallAverage = employees.length > 0 ?
        employees.reduce((sum, e) => sum + e.avgScore, 0) / employees.length : 0;

      return {
        success: true,
        data: {
          overview: {
            totalEmployees,
            employeesWithAssessments,
            overallAverage: Math.round(overallAverage * 100) / 100,
            assessmentParticipation: totalEmployees > 0 ?
              Math.round((employeesWithAssessments / totalEmployees) * 100) : 0
          },
          performanceDistribution,
          departmentAverages,
          topPerformers: employees
            .filter(e => e.avgScore > 0)
            .sort((a, b) => b.avgScore - a.avgScore)
            .slice(0, 10),
          improvementOpportunities: employees
            .filter(e => e.avgScore > 0 && e.avgScore < 70)
            .sort((a, b) => a.avgScore - b.avgScore)
            .slice(0, 10)
        }
      };
    } catch (error) {
      console.error('Error calculating performance analytics:', error);
      return {
        success: false,
        error: 'Failed to calculate performance analytics'
      };
    }
  }

  // 🔐 NIS2-COMPLIANT: Individual team performance data for widgets
  async getTeamPerformance(filters?: {
    teamId?: number;
    period?: string;
  }) {
    try {
      const endDate = new Date();
      const startDate = new Date();

      // Set date range based on period
      switch (filters?.period) {
        case '1month':
          startDate.setMonth(endDate.getMonth() - 1);
          break;
        case '6months':
          startDate.setMonth(endDate.getMonth() - 6);
          break;
        case '1year':
          startDate.setFullYear(endDate.getFullYear() - 1);
          break;
        default: // 3months
          startDate.setMonth(endDate.getMonth() - 3);
      }

      // Get team members and their performance data
      let usersQuery = this.usersRepository.createQueryBuilder('user')
        .leftJoinAndSelect('user.organizationalUnit', 'orgUnit');

      if (filters?.teamId) {
        usersQuery = usersQuery.where('orgUnit.id = :teamId', { teamId: filters.teamId });
      }

      const teamMembers = await usersQuery.getMany();

      if (teamMembers.length === 0) {
        return {
          success: true,
          data: {
            teamName: 'Team',
            averageScore: 0,
            memberCount: 0,
            completionRate: 0,
            topPerformers: [],
            needsAttention: []
          }
        };
      }

      // Get assessments for team members
      const memberIds = teamMembers.map(m => m.id);
      const assessments = await this.assessmentRepository.find({
        where: {
          employeeId: In(memberIds),
          status: In(['COMPLETED', 'APPROVED']),
          assessmentDate: Between(startDate, endDate)
        },
        relations: ['employee']
      });

      // Calculate team metrics
      const memberPerformance = teamMembers.map(member => {
        const memberAssessments = assessments.filter(a => a.employeeId === member.id);
        const avgScore = memberAssessments.length > 0
          ? memberAssessments.reduce((sum, a) => sum + (a.totalScore || 0), 0) / memberAssessments.length
          : 0;

        return {
          id: member.id,
          name: `${member.firstName} ${member.lastName}`,
          role: member.role || 'Team Member',
          performanceScore: Math.round(avgScore * 10) / 10,
          trend: 'stable', // Would calculate from historical data
          lastAssessment: memberAssessments.length > 0
            ? memberAssessments[memberAssessments.length - 1].assessmentDate.toISOString().split('T')[0]
            : null
        };
      });

      const validScores = memberPerformance.filter(m => m.performanceScore > 0);
      const averageScore = validScores.length > 0
        ? validScores.reduce((sum, m) => sum + m.performanceScore, 0) / validScores.length
        : 0;

      const completionRate = teamMembers.length > 0
        ? Math.round((validScores.length / teamMembers.length) * 100)
        : 0;

      // Top performers (score >= 80)
      const topPerformers = memberPerformance
        .filter(m => m.performanceScore >= 80)
        .sort((a, b) => b.performanceScore - a.performanceScore)
        .slice(0, 5);

      // Members needing attention (score < 70)
      const needsAttention = memberPerformance
        .filter(m => m.performanceScore > 0 && m.performanceScore < 70)
        .sort((a, b) => a.performanceScore - b.performanceScore)
        .slice(0, 5);

      const teamName = teamMembers[0]?.organizationalUnit?.name || 'Team';

      return {
        success: true,
        data: {
          teamName,
          averageScore: Math.round(averageScore * 10) / 10,
          memberCount: teamMembers.length,
          completionRate,
          topPerformers,
          needsAttention
        }
      };
    } catch (error) {
      console.error('Error getting team performance:', error);
      return {
        success: false,
        error: 'Failed to get team performance data'
      };
    }
  }

  // 🔐 NIS2-COMPLIANT: Team performance comparison with benchmarking
  async getTeamPerformanceComparison(filters?: {
    period?: string;
    includeInactive?: boolean;
  }) {
    try {
      const endDate = new Date();
      const startDate = new Date();

      switch (filters?.period) {
        case '1month':
          startDate.setMonth(endDate.getMonth() - 1);
          break;
        case '6months':
          startDate.setMonth(endDate.getMonth() - 6);
          break;
        case '1year':
          startDate.setFullYear(endDate.getFullYear() - 1);
          break;
        default:
          startDate.setMonth(endDate.getMonth() - 3);
      }

      // Use centralized database API instead of raw SQL
      const orgUnitsQuery = this.usersRepository.createQueryBuilder('user')
        .select([
          'orgUnit.id as teamId',
          'orgUnit.name as teamName',
          'orgUnit.parentId as parentId',
          'COUNT(DISTINCT user.id) as totalMembers'
        ])
        .leftJoin('user.organizationalUnit', 'orgUnit')
        .where('user.isActive = :isActive', { isActive: true })
        .andWhere('orgUnit.isActive = :orgActive', { orgActive: true })
        .groupBy('orgUnit.id, orgUnit.name, orgUnit.parentId')
        .having('COUNT(DISTINCT user.id) > 0');

      const orgUnits = await orgUnitsQuery.getRawMany();

      // Get assessment data for each team
      const teams = await Promise.all(orgUnits.map(async (orgUnit) => {
        const teamUsers = await this.usersRepository.find({
          where: { organizationalUnitId: orgUnit.teamId, isActive: true }
        });

        const userIds = teamUsers.map(u => u.id);
        const assessments = userIds.length > 0 ? await this.assessmentRepository.find({
          where: {
            employeeId: In(userIds),
            status: In(['COMPLETED', 'APPROVED']),
            assessmentDate: Between(startDate, endDate)
          }
        }) : [];

        const scores = assessments.map(a => a.totalScore || 0).filter(s => s > 0);
        const avgScore = scores.length > 0 ? scores.reduce((sum, s) => sum + s, 0) / scores.length : 0;
        const minScore = scores.length > 0 ? Math.min(...scores) : 0;
        const maxScore = scores.length > 0 ? Math.max(...scores) : 0;
        const excellentPerformers = scores.filter(s => s >= 90).length;
        const underPerformers = scores.filter(s => s < 60).length;

        return {
          teamId: orgUnit.teamId,
          teamName: orgUnit.teamName,
          parentId: orgUnit.parentId,
          totalMembers: parseInt(orgUnit.totalMembers),
          totalAssessments: assessments.length,
          avgScore,
          minScore,
          maxScore,
          excellentPerformers,
          underPerformers
        };
      }));

      // Sort by average score descending
      teams.sort((a, b) => (b.avgScore || 0) - (a.avgScore || 0));

      // Calculate benchmarks
      const allScores = teams.filter(t => t.avgScore).map(t => t.avgScore);
      const industryBenchmark = 75; // This could come from external data
      const companyAverage = allScores.length > 0 ?
        allScores.reduce((sum, score) => sum + score, 0) / allScores.length : 0;

      // Enhance team data with comparisons
      const enhancedTeams = teams.map(team => {
        const score = team.avgScore || 0;
        return {
          ...team,
          avgScore: Math.round(score * 100) / 100,
          vsCompanyAverage: Math.round((score - companyAverage) * 100) / 100,
          vsIndustryBenchmark: Math.round((score - industryBenchmark) * 100) / 100,
          participationRate: team.totalMembers > 0 ?
            Math.round((team.totalAssessments / team.totalMembers) * 100) : 0,
          performanceLevel: score >= 90 ? 'excellent' :
            score >= 75 ? 'good' :
              score >= 60 ? 'satisfactory' : 'needs-improvement'
        };
      });

      return {
        success: true,
        data: {
          teams: enhancedTeams,
          benchmarks: {
            companyAverage: Math.round(companyAverage * 100) / 100,
            industryBenchmark,
            topTeamScore: enhancedTeams.length > 0 ? enhancedTeams[0].avgScore : 0
          },
          insights: {
            totalTeams: enhancedTeams.length,
            teamsAboveAverage: enhancedTeams.filter(t => t.avgScore > companyAverage).length,
            teamsAboveBenchmark: enhancedTeams.filter(t => t.avgScore > industryBenchmark).length,
            highPerformingTeams: enhancedTeams.filter(t => t.performanceLevel === 'excellent').length
          }
        }
      };
    } catch (error) {
      console.error('Error calculating team performance comparison:', error);
      return {
        success: false,
        error: 'Failed to calculate team performance comparison'
      };
    }
  }

}
