import { TypeOrmModuleOptions } from '@nestjs/typeorm';

/**
 * 🔐 Database Optimization Configuration
 * 
 * Provides optimized database connection settings for production environments
 * with focus on performance, security, and reliability.
 */
export class DatabaseOptimizationConfig {

  /**
   * Get optimized TypeORM configuration for production
   */
  static getOptimizedConfig(baseConfig: TypeOrmModuleOptions): TypeOrmModuleOptions {
    return {
      ...baseConfig,

      // Connection Pool Optimization
      extra: {
        // Connection pool settings for high-performance applications
        connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT || '20'),
        acquireTimeout: parseInt(process.env.DB_ACQUIRE_TIMEOUT || '60000'), // 60 seconds
        timeout: parseInt(process.env.DB_TIMEOUT || '60000'), // 60 seconds

        // MySQL-specific optimizations
        charset: 'utf8mb4',
        timezone: 'Z', // UTC timezone

        // Security settings
        ssl: process.env.NODE_ENV === 'production' ? {
          rejectUnauthorized: process.env.DB_SSL_REJECT_UNAUTHORIZED !== 'false'
        } : false,

        // Performance optimizations
        supportBigNumbers: true,
        bigNumberStrings: true,
        dateStrings: false,

        // Connection management
        reconnect: true,
        idleTimeout: parseInt(process.env.DB_IDLE_TIMEOUT || '300000'), // 5 minutes

        // Query optimization
        multipleStatements: false, // Security: prevent SQL injection

        ...baseConfig.extra,
      },

      // Connection pool configuration (removed poolSize for TypeORM 0.3 compatibility)

      // Query optimization
      cache: {
        type: 'database',
        duration: parseInt(process.env.DB_CACHE_DURATION || '30000'), // 30 seconds
        tableName: 'query_result_cache',
      },

      // Logging optimization for production
      logging: process.env.NODE_ENV === 'production' ? ['error', 'warn'] : ['query', 'error', 'warn'],

      // Performance monitoring
      maxQueryExecutionTime: parseInt(process.env.DB_MAX_QUERY_TIME || '5000'), // 5 seconds

      // Migration settings
      migrationsRun: false, // Run migrations manually in production
      synchronize: false, // Never auto-sync in production

      // Entity loading optimization
      entityPrefix: process.env.DB_ENTITY_PREFIX || '',

      // Connection retry configuration
      retryAttempts: parseInt(process.env.DB_RETRY_ATTEMPTS || '3'),
      retryDelay: parseInt(process.env.DB_RETRY_DELAY || '3000'), // 3 seconds
    };
  }

  /**
   * Get database health check configuration
   */
  static getHealthCheckConfig() {
    return {
      // Health check query timeout
      timeout: parseInt(process.env.DB_HEALTH_TIMEOUT || '5000'), // 5 seconds

      // Health check interval
      interval: parseInt(process.env.DB_HEALTH_INTERVAL || '30000'), // 30 seconds

      // Health check query
      query: 'SELECT 1 as health_check',

      // Connection validation
      validateConnection: true,
    };
  }

  /**
   * Get performance monitoring configuration
   */
  static getPerformanceConfig() {
    return {
      // Slow query threshold
      slowQueryThreshold: parseInt(process.env.DB_SLOW_QUERY_THRESHOLD || '1000'), // 1 second

      // Query logging
      logQueries: process.env.DB_LOG_QUERIES === 'true',

      // Performance metrics
      enableMetrics: process.env.DB_ENABLE_METRICS === 'true',

      // Connection monitoring
      monitorConnections: process.env.DB_MONITOR_CONNECTIONS === 'true',
    };
  }

  /**
   * Validate database configuration
   */
  static validateConfig(): void {
    const requiredEnvVars = [
      'DB_HOST',
      'DB_PORT',
      'DB_USERNAME',
      'DB_PASSWORD',
      'DB_NAME'
    ];

    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

    if (missingVars.length > 0) {
      throw new Error(`🔐 [DATABASE] Missing required environment variables: ${missingVars.join(', ')}`);
    }

    // Validate connection limits
    const connectionLimit = parseInt(process.env.DB_CONNECTION_LIMIT || '20');
    if (connectionLimit < 5 || connectionLimit > 100) {
      console.warn('🔐 [DATABASE] Warning: DB_CONNECTION_LIMIT should be between 5 and 100');
    }

    // Validate pool size
    const poolSize = parseInt(process.env.DB_POOL_SIZE || '10');
    if (poolSize < 2 || poolSize > connectionLimit) {
      console.warn('🔐 [DATABASE] Warning: DB_POOL_SIZE should be between 2 and DB_CONNECTION_LIMIT');
    }

    console.log('🔐 [DATABASE] Configuration validated successfully', {
      connectionLimit,
      poolSize,
      environment: process.env.NODE_ENV,
      ssl: process.env.NODE_ENV === 'production'
    });
  }
}

/**
 * Environment-specific database configurations
 */
export const DatabaseEnvironmentConfig = {
  development: {
    connectionLimit: 10,
    poolSize: 5,
    logging: true,
    cache: false,
  },

  test: {
    connectionLimit: 5,
    poolSize: 2,
    logging: false,
    cache: false,
  },

  production: {
    connectionLimit: 20,
    poolSize: 10,
    logging: false,
    cache: true,
  },
};
