{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "es2017", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "strict": false, "noImplicitReturns": false, "noUnusedLocals": false, "noUnusedParameters": false}, "exclude": ["**/*.ghost/**", "test.ghost/**", "src/**/*.ghost/**", "src/**/__tests__.ghost/**", "templates/**"]}