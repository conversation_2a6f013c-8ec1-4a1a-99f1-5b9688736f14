#!/bin/bash

# EHRX Application - Stop All Services Script
# This script stops all running EHRX services

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
FRONTEND_PORT=3080
BACKEND_PORT=4000

echo -e "${CYAN}🛑 EHRX Application - Stopping All Services${NC}"
echo -e "${CYAN}=============================================${NC}"

# Function to kill processes on a specific port
kill_port() {
    local port=$1
    local service_name=$2
    
    echo -e "${BLUE}🔍 Checking for processes on port $port ($service_name)...${NC}"
    
    local pids=$(lsof -ti:$port 2>/dev/null)
    
    if [ -n "$pids" ]; then
        echo -e "${YELLOW}🔄 Killing $service_name processes on port $port...${NC}"
        echo "$pids" | xargs kill -TERM 2>/dev/null || true
        
        # Wait a moment for graceful shutdown
        sleep 3
        
        # Force kill if still running
        local remaining_pids=$(lsof -ti:$port 2>/dev/null)
        if [ -n "$remaining_pids" ]; then
            echo -e "${YELLOW}⚡ Force killing remaining $service_name processes...${NC}"
            echo "$remaining_pids" | xargs kill -9 2>/dev/null || true
        fi
        
        echo -e "${GREEN}✅ $service_name stopped${NC}"
    else
        echo -e "${GREEN}✅ No $service_name processes found on port $port${NC}"
    fi
}

# Function to kill processes by name pattern
kill_by_pattern() {
    local pattern=$1
    local service_name=$2
    
    echo -e "${BLUE}🔍 Checking for $service_name processes...${NC}"
    
    local pids=$(pgrep -f "$pattern" 2>/dev/null || true)
    
    if [ -n "$pids" ]; then
        echo -e "${YELLOW}🔄 Killing $service_name processes...${NC}"
        echo "$pids" | xargs kill -TERM 2>/dev/null || true
        
        # Wait a moment for graceful shutdown
        sleep 2
        
        # Force kill if still running
        local remaining_pids=$(pgrep -f "$pattern" 2>/dev/null || true)
        if [ -n "$remaining_pids" ]; then
            echo -e "${YELLOW}⚡ Force killing remaining $service_name processes...${NC}"
            echo "$remaining_pids" | xargs kill -9 2>/dev/null || true
        fi
        
        echo -e "${GREEN}✅ $service_name processes stopped${NC}"
    else
        echo -e "${GREEN}✅ No $service_name processes found${NC}"
    fi
}

# Stop services by port
kill_port $BACKEND_PORT "Backend (NestJS)"
kill_port $FRONTEND_PORT "Frontend (React)"

# Stop services by process pattern (backup method)
echo -e "\n${BLUE}🔄 Checking for remaining processes by pattern...${NC}"
kill_by_pattern "nest start" "NestJS Backend"
kill_by_pattern "react-scripts start" "React Frontend"
kill_by_pattern "node.*backend" "Node Backend"

# Clean up any remaining npm/node processes related to the project
echo -e "\n${BLUE}🧹 Cleaning up project-related processes...${NC}"
pkill -f "/var/www/ehrx" 2>/dev/null || true

# Verify all services are stopped
echo -e "\n${BLUE}🔍 Verifying services are stopped...${NC}"

if lsof -Pi :$BACKEND_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo -e "${RED}⚠️  Warning: Something is still running on port $BACKEND_PORT${NC}"
else
    echo -e "${GREEN}✅ Port $BACKEND_PORT is free${NC}"
fi

if lsof -Pi :$FRONTEND_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo -e "${RED}⚠️  Warning: Something is still running on port $FRONTEND_PORT${NC}"
else
    echo -e "${GREEN}✅ Port $FRONTEND_PORT is free${NC}"
fi

echo -e "\n${GREEN}🎉 All EHRX services have been stopped!${NC}"
echo -e "${CYAN}💡 To start services again, run: ./start-all-services.sh${NC}"
