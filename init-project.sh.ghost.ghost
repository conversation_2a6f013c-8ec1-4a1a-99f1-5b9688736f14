#!/bin/bash

echo " ===== EHRX Project Initialization =====\
echo \Setting up project directories...\

# Make sure directories exist
mkdir -p /var/www/ehrx/backend
mkdir -p /var/www/ehrx/frontend
mkdir -p /var/www/ehrx/database
mkdir -p /var/www/ehrx/docs

# Create basic README files with descriptions
echo \Creating documentation files...\

# Backend README
cat > /var/www/ehrx/backend/README.md << 'EOF'
# EHRX Backend

NestJS-based backend API for the Employee Performance Management Dashboard.

## Features
- REST API for employee performance management
- Authentication with JWT
- Role-based access control
- Database integration with TypeORM
- Performance analytics endpoints

## Tech Stack
- NestJS
- TypeScript
- TypeORM
- MySQL/MariaDB
- JWT Authentication
EOF

# Frontend README
cat > /var/www/ehrx/frontend/README.md << 'EOF'
# EHRX Frontend

React-based frontend for the Employee Performance Management Dashboard.

## Features
- Interactive performance dashboards
- Assessment forms and templates
- Team management interface
- Analytics visualization
- Role-based UI components

## Tech Stack
- React
- TypeScript
- Material UI / React components
- Chart.js for visualization
- Axios for API communication
EOF

# Database README
cat > /var/www/ehrx/database/README.md << 'EOF'
# EHRX Database

Database migrations and schema for the Employee Performance Management Dashboard.

## Schema Overview
- Users and authentication
- Teams and team membership
- Assessment templates
- Performance reviews
- Action items and trackers
EOF

# Create .gitignore files
echo \Creating .gitignore files...\

cat > /var/www/ehrx/.gitignore << 'EOF'
# Dependencies
node_modules/
.pnp
.pnp.js

# Production builds
dist/
build/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Editor directories and files
.idea/
.vscode/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db
EOF

echo \===== Initialization Complete =====\
echo \Next steps:\
echo \1. Run cd /var/www/ehrx/backend && npm init -y to initialize backend package.json\
echo \2. Run cd /var/www/ehrx/frontend && npm init -y to initialize frontend package.json\
echo \3. Install required dependencies\
