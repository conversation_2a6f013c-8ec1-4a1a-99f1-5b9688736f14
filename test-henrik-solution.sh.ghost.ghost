#!/bin/bash

# Test script to verify <PERSON> solution
echo "🎯 TESTING HENRIK THOMSEN SOLUTION"
echo "=================================="
echo ""

# Check if <PERSON> exists in the database
echo "🗄️ Checking <PERSON> in database..."
HENRIK_DB=$(mysql -h 127.0.0.1 -u debian-sys-maint -pa500cd12558364e3f91966f73b1fe8ea009531ff669bf74c ehrx -e "SELECT COUNT(*) as count FROM users WHERE first_name = '<PERSON>';" 2>/dev/null | tail -1)

if [ "$HENRIK_DB" = "1" ]; then
    echo "✅ <PERSON> found in database!"
    mysql -h 127.0.0.1 -u debian-sys-maint -pa500cd12558364e3f91966f73b1fe8ea009531ff669bf74c ehrx -e "SELECT id, first_name, last_name, email, role FROM users WHERE first_name = '<PERSON>';" 2>/dev/null
else
    echo "❌ <PERSON> NOT found in database"
fi

echo ""

# Check demo server endpoints
echo "📡 Checking demo server endpoints..."
echo "Demo server users endpoint:"
if curl -s http://localhost:3080/api/users | grep -q "Henrik"; then
    echo "✅ Henrik found in demo server users"
else
    echo "❌ Henrik NOT found in demo server users"
fi

echo ""
echo "Demo server database tables endpoint:"
if curl -s http://localhost:3080/api/database/tables/users | grep -q "Henrik"; then
    echo "✅ Henrik found in demo server database tables"
else
    echo "❌ Henrik NOT found in demo server database tables"
fi

echo ""

# Test if we can start a simple backend
echo "🔧 Testing backend connectivity..."
if curl -s http://localhost:4000/auth/health > /dev/null; then
    echo "✅ Backend is running on port 4000"
    
    # Test the new database endpoint
    echo "Testing new database endpoint..."
    curl -s http://localhost:4000/database/tables/users | head -20
else
    echo "❌ Backend not running on port 4000"
fi

echo ""
echo "🎯 SOLUTION STATUS:"
echo "=================="
echo "1. Henrik Thomsen is now in the real database ✅"
echo "2. Need to connect Settings dashboard to real backend"
echo "3. Two options:"
echo "   a) Start real NestJS backend (port 4000) ← Preferred"
echo "   b) Update demo server to read from real database"
echo ""
echo "💡 NEXT STEPS:"
echo "============="
echo "1. Fix backend startup issues"
echo "2. Update frontend to use real backend"
echo "3. Henrik will appear in Settings dashboard"
echo ""
echo "🔍 Henrik's details in database:"
mysql -h 127.0.0.1 -u debian-sys-maint -pa500cd12558364e3f91966f73b1fe8ea009531ff669bf74c ehrx -e "SELECT * FROM users WHERE first_name = 'Henrik';" 2>/dev/null
