const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');

const app = express();
const PORT = 80;

// Simple proxy to React dev server
app.use('/', createProxyMiddleware({
  target: 'http://localhost:3080',
  changeOrigin: true,
  ws: true,
  logLevel: 'silent'
}));

app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 EHRX Application Server running on port ${PORT}`);
  console.log(`🌐 Access via: http://dev.trusthansen.dk`);
  console.log(`📱 External access: http://*************`);
}).on('error', (err) => {
  if (err.code === 'EACCES') {
    console.log('❌ Cannot bind to port 80 (requires sudo)');
    console.log('✅ Use port 8080 instead: http://dev.trusthansen.dk:8080');
  } else {
    console.error('Server error:', err);
  }
});
