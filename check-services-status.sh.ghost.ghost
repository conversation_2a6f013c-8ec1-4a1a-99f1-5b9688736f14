#!/bin/bash

# EHRX Application - Service Status Check Script
# This script checks the status of all EHRX services

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
FRONTEND_PORT=3080
BACKEND_PORT=4000

echo -e "${CYAN}📊 EHRX Application - Service Status Check${NC}"
echo -e "${CYAN}===========================================${NC}"

# Function to check service status
check_service() {
    local port=$1
    local service_name=$2
    local endpoint=$3
    
    echo -e "\n${BLUE}🔍 Checking $service_name (Port $port)${NC}"
    
    # Check if port is listening
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "  ${GREEN}✅ Port Status:${NC} Listening on $port"
        
        # Get process info
        local pid=$(lsof -ti:$port 2>/dev/null | head -1)
        if [ -n "$pid" ]; then
            local process_info=$(ps -p $pid -o pid,ppid,cmd --no-headers 2>/dev/null)
            echo -e "  ${GREEN}📋 Process:${NC} $process_info"
        fi
        
        # Check HTTP response if endpoint provided
        if [ -n "$endpoint" ]; then
            local response_code=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:$port$endpoint" 2>/dev/null)
            if [ "$response_code" = "200" ] || [ "$response_code" = "404" ] || [ "$response_code" = "302" ]; then
                echo -e "  ${GREEN}🌐 HTTP Status:${NC} $response_code (Service responding)"
            else
                echo -e "  ${YELLOW}🌐 HTTP Status:${NC} $response_code (Service may be starting)"
            fi
        fi
        
        return 0
    else
        echo -e "  ${RED}❌ Port Status:${NC} Not listening on $port"
        return 1
    fi
}

# Function to check URL accessibility
check_url() {
    local url=$1
    local name=$2
    
    echo -e "\n${BLUE}🔍 Checking $name${NC}"
    
    local response_code=$(curl -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null)
    if [ "$response_code" = "200" ] || [ "$response_code" = "302" ]; then
        echo -e "  ${GREEN}✅ URL Status:${NC} $response_code (Accessible)"
        return 0
    else
        echo -e "  ${RED}❌ URL Status:${NC} $response_code (Not accessible)"
        return 1
    fi
}

# Check Backend Service
backend_status=0
check_service $BACKEND_PORT "Backend API (NestJS)" "/api" || backend_status=1

# Check Frontend Service
frontend_status=0
check_service $FRONTEND_PORT "Frontend App (React)" "/" || frontend_status=1

# Check Public URLs
echo -e "\n${PURPLE}🌍 Public URL Accessibility${NC}"
public_status=0
check_url "https://dev.trusthansen.dk/" "Public Frontend" || public_status=1
check_url "https://dev.trusthansen.dk/api" "Public API" || public_status=1

# Check specific API endpoints
if [ $backend_status -eq 0 ]; then
    echo -e "\n${PURPLE}🔧 API Endpoint Health${NC}"
    
    # Check auth endpoint
    auth_response=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:$BACKEND_PORT/api/auth/login" -X POST -H "Content-Type: application/json" -d '{}' 2>/dev/null)
    if [ "$auth_response" = "400" ] || [ "$auth_response" = "401" ]; then
        echo -e "  ${GREEN}✅ Auth Endpoint:${NC} $auth_response (Responding correctly)"
    else
        echo -e "  ${YELLOW}⚠️  Auth Endpoint:${NC} $auth_response (Unexpected response)"
    fi
    
    # Check users endpoint (should be 401 without auth)
    users_response=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:$BACKEND_PORT/api/users" 2>/dev/null)
    if [ "$users_response" = "401" ]; then
        echo -e "  ${GREEN}✅ Users Endpoint:${NC} $users_response (Protected correctly)"
    else
        echo -e "  ${YELLOW}⚠️  Users Endpoint:${NC} $users_response (Unexpected response)"
    fi
    
    # Check API docs
    docs_response=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:$BACKEND_PORT/api/docs" 2>/dev/null)
    if [ "$docs_response" = "200" ] || [ "$docs_response" = "302" ]; then
        echo -e "  ${GREEN}✅ API Docs:${NC} $docs_response (Available)"
    else
        echo -e "  ${YELLOW}⚠️  API Docs:${NC} $docs_response (Not available)"
    fi
fi

# Overall status summary
echo -e "\n${CYAN}📋 Overall Status Summary${NC}"
echo -e "${CYAN}=========================${NC}"

if [ $backend_status -eq 0 ]; then
    echo -e "${GREEN}✅ Backend Service:${NC} Running on port $BACKEND_PORT"
else
    echo -e "${RED}❌ Backend Service:${NC} Not running"
fi

if [ $frontend_status -eq 0 ]; then
    echo -e "${GREEN}✅ Frontend Service:${NC} Running on port $FRONTEND_PORT"
else
    echo -e "${RED}❌ Frontend Service:${NC} Not running"
fi

if [ $public_status -eq 0 ]; then
    echo -e "${GREEN}✅ Public Access:${NC} Available at https://dev.trusthansen.dk/"
else
    echo -e "${RED}❌ Public Access:${NC} Not available"
fi

# Quick action suggestions
echo -e "\n${YELLOW}💡 Quick Actions:${NC}"
if [ $backend_status -ne 0 ] || [ $frontend_status -ne 0 ]; then
    echo -e "  Start all services:   ${CYAN}./start-all-services.sh${NC}"
    echo -e "  Restart all services: ${CYAN}./restart-all-services.sh${NC}"
else
    echo -e "  Stop all services:    ${CYAN}./stop-all-services.sh${NC}"
    echo -e "  Restart all services: ${CYAN}./restart-all-services.sh${NC}"
fi

echo -e "  View this status:     ${CYAN}./check-services-status.sh${NC}"

# Exit with appropriate code
if [ $backend_status -eq 0 ] && [ $frontend_status -eq 0 ]; then
    echo -e "\n${GREEN}🎉 All core services are running!${NC}"
    exit 0
else
    echo -e "\n${RED}⚠️  Some services are not running${NC}"
    exit 1
fi
