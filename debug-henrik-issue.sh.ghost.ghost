#!/bin/bash

# Debug script to understand <PERSON> issue
echo "🔍 DEBUGGING HENRIK THOMSEN ISSUE"
echo "================================="
echo ""

# Check what's actually running
echo "📡 Checking running processes..."
echo "Demo server (port 3001):"
curl -s http://localhost:3001/api/users | head -20

echo ""
echo "Frontend (port 3000):"
curl -s http://localhost:3000 > /dev/null && echo "✅ Frontend is running" || echo "❌ Frontend not running"

echo ""
echo "Backend (port 4000):"
curl -s http://localhost:4000/auth/health > /dev/null && echo "✅ Backend is running" || echo "❌ Backend not running"

echo ""

# Check the demo server users endpoint
echo "👥 Demo server users (what Organization dashboard uses):"
curl -s http://localhost:3001/api/users | jq '.' 2>/dev/null || curl -s http://localhost:3001/api/users

echo ""

# Check the demo server database tables endpoint
echo "🗄️ Demo server database tables (what Settings dashboard uses):"
curl -s http://localhost:3001/api/database/tables/users | jq '.' 2>/dev/null || curl -s http://localhost:3001/api/database/tables/users

echo ""

# Check if Henrik exists in either
echo "🔍 Searching for Henrik in demo server users:"
if curl -s http://localhost:3001/api/users | grep -i "henrik"; then
    echo "✅ Henrik found in demo server users"
else
    echo "❌ Henrik NOT found in demo server users"
fi

echo ""
echo "🔍 Searching for Henrik in demo server database tables:"
if curl -s http://localhost:3001/api/database/tables/users | grep -i "henrik"; then
    echo "✅ Henrik found in demo server database tables"
else
    echo "❌ Henrik NOT found in demo server database tables"
fi

echo ""
echo "🎯 ANALYSIS:"
echo "============"
echo "1. The Organization dashboard uses /api/users (demo server)"
echo "2. The Settings dashboard uses /api/database/tables/users (demo server)"
echo "3. Both are mock endpoints - no real database connection!"
echo "4. Henrik was probably added to the frontend state but not persisted"
echo ""
echo "💡 SOLUTION:"
echo "============"
echo "1. Start the real NestJS backend on port 4000"
echo "2. Update frontend to use real backend APIs"
echo "3. Create Henrik through the real backend API"
echo "4. Settings dashboard will then show real database data"
