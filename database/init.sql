-- EHRX Enhanced Database Initialization Script
-- This script creates the complete, enhanced database schema with all security features
-- Consolidates all previous migration scripts into a single, authoritative source

-- Create database
CREATE DATABASE IF NOT EXISTS ehrx CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE ehrx;

-- =====================================================
-- ORGANIZATIONAL STRUCTURE TABLES
-- =====================================================

-- Organizational Units table
CREATE TABLE IF NOT EXISTS organizational_units (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    parent_id INT NULL,
    created_at DATETIME(6) DEFAULT CURRENT_TIMESTAMP(6),
    updated_at DATETIME(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    FOREIGN KEY (parent_id) REFERENCES organizational_units(id) ON DELETE SET NULL,
    INDEX idx_parent_id (parent_id)
) ENGINE=InnoDB;

-- =====================================================
-- ENHANCED USERS TABLE (NIS2 COMPLIANT)
-- =====================================================

CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(255) NOT NULL,
    last_name VARCHAR(255) NOT NULL,
    title VARCHAR(255),
    role ENUM('ceo', 'vp', 'director', 'manager', 'senior_engineer', 'engineer', 'junior_engineer', 'intern', 'hr_admin', 'guest', 'employee') NOT NULL DEFAULT 'engineer',
    organizational_unit_id INT NULL,
    manager_id INT NULL,
    hire_date DATE,
    salary DECIMAL(10,2),
    employment_type ENUM('full_time', 'part_time', 'contract', 'intern') NOT NULL DEFAULT 'full_time',
    location VARCHAR(255),
    phone VARCHAR(255),
    emergency_contact_name VARCHAR(255),
    emergency_contact_phone VARCHAR(255),
    
    -- Basic status fields
    is_active TINYINT NOT NULL DEFAULT 1,
    
    -- Enhanced Security Fields (NIS2 Compliance)
    account_status ENUM('active', 'inactive', 'locked', 'pending_activation', 'suspended') NOT NULL DEFAULT 'active',
    failed_login_attempts INT NOT NULL DEFAULT 0,
    last_login_at TIMESTAMP NULL,
    last_login_ip VARCHAR(255),
    password_changed_at TIMESTAMP NULL,
    must_change_password TINYINT NOT NULL DEFAULT 1,
    account_locked_until TIMESTAMP NULL,
    
    -- Two-Factor Authentication
    two_factor_enabled TINYINT NOT NULL DEFAULT 0,
    two_factor_secret VARCHAR(255) NULL,
    
    -- Session Management (Standardized with TypeORM Entity)
    session_token VARCHAR(1000) NULL,
    session_expires_at TIMESTAMP NULL,

    -- Refresh Token Management
    refresh_token VARCHAR(1000) NULL,
    refresh_token_expires_at TIMESTAMP NULL,

    -- MFA and Security Features
    mfa_backup_codes TEXT NULL,
    password_history TEXT NULL,
    
    -- Timestamps
    created_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    updated_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    
    -- Foreign Keys
    FOREIGN KEY (organizational_unit_id) REFERENCES organizational_units(id) ON DELETE SET NULL,
    FOREIGN KEY (manager_id) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Indexes for performance and security
    INDEX idx_email (email),
    INDEX idx_account_status (account_status),
    INDEX idx_session_token (session_token(255)),
    INDEX idx_refresh_token (refresh_token(255)),
    INDEX idx_manager_id (manager_id),
    INDEX idx_organizational_unit_id (organizational_unit_id),
    INDEX idx_role (role),
    INDEX idx_is_active (is_active),
    INDEX idx_last_login_at (last_login_at),
    INDEX idx_failed_login_attempts (failed_login_attempts),
    INDEX idx_two_factor_enabled (two_factor_enabled)
) ENGINE=InnoDB;

-- =====================================================
-- SESSION MANAGEMENT TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS user_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    session_token TEXT NOT NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_session_token_unique (session_token(255)),
    INDEX idx_expires_at (expires_at),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB;

-- =====================================================
-- TEAMS AND SKILLSETS
-- =====================================================

-- Teams table
CREATE TABLE IF NOT EXISTS teams (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_created_by (created_by)
) ENGINE=InnoDB;

-- Team Membership table
CREATE TABLE IF NOT EXISTS team_members (
    id INT AUTO_INCREMENT PRIMARY KEY,
    team_id INT NOT NULL,
    user_id INT NOT NULL,
    role ENUM('team_lead', 'member', 'guest') NOT NULL DEFAULT 'member',
    added_at DATETIME(6) DEFAULT CURRENT_TIMESTAMP(6),
    UNIQUE KEY unique_team_user (team_id, user_id),
    FOREIGN KEY (team_id) REFERENCES teams(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_team_id (team_id),
    INDEX idx_user_id (user_id)
) ENGINE=InnoDB;

-- Skillsets table
CREATE TABLE IF NOT EXISTS skillsets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    created_at DATETIME(6) DEFAULT CURRENT_TIMESTAMP(6),
    updated_at DATETIME(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    INDEX idx_category (category)
) ENGINE=InnoDB;

-- User Skillsets table
CREATE TABLE IF NOT EXISTS user_skillsets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    skillset_id INT NOT NULL,
    proficiency_level ENUM('beginner', 'intermediate', 'advanced', 'expert') NOT NULL,
    certification_name VARCHAR(255),
    certification_date DATE,
    expiry_date DATE,
    created_at DATETIME(6) DEFAULT CURRENT_TIMESTAMP(6),
    updated_at DATETIME(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (skillset_id) REFERENCES skillsets(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_skillset (user_id, skillset_id),
    INDEX idx_user_id (user_id),
    INDEX idx_skillset_id (skillset_id),
    INDEX idx_proficiency_level (proficiency_level)
) ENGINE=InnoDB;

-- =====================================================
-- ASSESSMENT SYSTEM TABLES
-- =====================================================

-- Assessment Templates table
CREATE TABLE IF NOT EXISTS assessment_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_by INT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_created_by (created_by),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB;

-- Assessment Areas table
CREATE TABLE IF NOT EXISTS assessment_areas (
    id INT AUTO_INCREMENT PRIMARY KEY,
    template_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    weight DECIMAL(5,2) DEFAULT 1.00,
    order_index INT DEFAULT 0,
    FOREIGN KEY (template_id) REFERENCES assessment_templates(id) ON DELETE CASCADE,
    INDEX idx_template_id (template_id),
    INDEX idx_order_index (order_index)
) ENGINE=InnoDB;

-- Assessment Criteria table
CREATE TABLE IF NOT EXISTS assessment_criteria (
    id INT AUTO_INCREMENT PRIMARY KEY,
    area_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    weight DECIMAL(5,2) DEFAULT 1.00,
    order_index INT DEFAULT 0,
    FOREIGN KEY (area_id) REFERENCES assessment_areas(id) ON DELETE CASCADE,
    INDEX idx_area_id (area_id),
    INDEX idx_order_index (order_index)
) ENGINE=InnoDB;

-- Assessment Instances table
CREATE TABLE IF NOT EXISTS assessment_instances (
    id INT AUTO_INCREMENT PRIMARY KEY,
    template_id INT NOT NULL,
    assessed_user_id INT NOT NULL,
    assessor_user_id INT NOT NULL,
    status ENUM('draft', 'in_progress', 'completed', 'reviewed') DEFAULT 'draft',
    assessment_period_start DATE,
    assessment_period_end DATE,
    due_date DATE,
    completed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (template_id) REFERENCES assessment_templates(id),
    FOREIGN KEY (assessed_user_id) REFERENCES users(id),
    FOREIGN KEY (assessor_user_id) REFERENCES users(id),
    INDEX idx_template_id (template_id),
    INDEX idx_assessed_user_id (assessed_user_id),
    INDEX idx_assessor_user_id (assessor_user_id),
    INDEX idx_status (status),
    INDEX idx_due_date (due_date)
) ENGINE=InnoDB;

-- Assessment Responses table
CREATE TABLE IF NOT EXISTS assessment_responses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    instance_id INT NOT NULL,
    criteria_id INT NOT NULL,
    score DECIMAL(5,2),
    comments TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (instance_id) REFERENCES assessment_instances(id) ON DELETE CASCADE,
    FOREIGN KEY (criteria_id) REFERENCES assessment_criteria(id),
    UNIQUE KEY unique_instance_criteria (instance_id, criteria_id),
    INDEX idx_instance_id (instance_id),
    INDEX idx_criteria_id (criteria_id),
    INDEX idx_score (score)
) ENGINE=InnoDB;

-- =====================================================
-- PERFORMANCE MANAGEMENT TABLES
-- =====================================================

-- Analytics Dashboards table
CREATE TABLE IF NOT EXISTS analytics_dashboards (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    user_id INT NOT NULL,
    dashboard_config JSON,
    is_public BOOLEAN DEFAULT FALSE,
    created_at DATETIME(6) DEFAULT CURRENT_TIMESTAMP(6),
    updated_at DATETIME(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_is_public (is_public)
) ENGINE=InnoDB;

-- Performance Metrics table
CREATE TABLE IF NOT EXISTS performance_metrics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    metric_name VARCHAR(255) NOT NULL,
    metric_value DECIMAL(10,2) NOT NULL,
    metric_type ENUM('productivity', 'quality', 'collaboration', 'innovation', 'leadership') NOT NULL,
    measurement_period_start DATE NOT NULL,
    measurement_period_end DATE NOT NULL,
    created_at DATETIME(6) DEFAULT CURRENT_TIMESTAMP(6),
    updated_at DATETIME(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_metric_type (metric_type),
    INDEX idx_measurement_period (measurement_period_start, measurement_period_end)
) ENGINE=InnoDB;

-- Recognition Badges table (matches existing database structure)
CREATE TABLE IF NOT EXISTS recognition_badges (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    icon_url VARCHAR(500),
    badge_type ENUM('achievement', 'appreciation', 'milestone', 'skill') NOT NULL,
    point_value INT NOT NULL DEFAULT 0,
    criteria JSON,
    is_active TINYINT NOT NULL DEFAULT 1,
    created_by_id INT NOT NULL,
    created_at DATETIME(6) DEFAULT CURRENT_TIMESTAMP(6),
    FOREIGN KEY (created_by_id) REFERENCES users(id),
    INDEX idx_badge_type (badge_type),
    INDEX idx_point_value (point_value),
    INDEX idx_created_by_id (created_by_id)
) ENGINE=InnoDB;

-- Recognition Instances table
CREATE TABLE IF NOT EXISTS recognition_instances (
    id INT AUTO_INCREMENT PRIMARY KEY,
    badge_id INT NOT NULL,
    recipient_user_id INT NOT NULL,
    awarded_by_user_id INT NOT NULL,
    reason TEXT,
    awarded_at DATETIME(6) DEFAULT CURRENT_TIMESTAMP(6),
    FOREIGN KEY (badge_id) REFERENCES recognition_badges(id) ON DELETE CASCADE,
    FOREIGN KEY (recipient_user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (awarded_by_user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_badge_id (badge_id),
    INDEX idx_recipient_user_id (recipient_user_id),
    INDEX idx_awarded_by_user_id (awarded_by_user_id),
    INDEX idx_awarded_at (awarded_at)
) ENGINE=InnoDB;

-- User Gamification table
CREATE TABLE IF NOT EXISTS user_gamification (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    total_points INT DEFAULT 0,
    level_name VARCHAR(100) DEFAULT 'Beginner',
    achievements_count INT DEFAULT 0,
    last_activity_at DATETIME(6) DEFAULT CURRENT_TIMESTAMP(6),
    created_at DATETIME(6) DEFAULT CURRENT_TIMESTAMP(6),
    updated_at DATETIME(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_gamification (user_id),
    INDEX idx_total_points (total_points),
    INDEX idx_level_name (level_name)
) ENGINE=InnoDB;

-- =====================================================
-- STORED PROCEDURES AND EVENTS
-- =====================================================

-- Create stored procedure to clean up expired sessions
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS CleanupExpiredSessions()
BEGIN
    DELETE FROM user_sessions WHERE expires_at < NOW();
    UPDATE users SET session_token = NULL, session_expires_at = NULL
    WHERE session_expires_at < NOW();
END //
DELIMITER ;

-- Create event to automatically clean up expired sessions every hour
SET GLOBAL event_scheduler = ON;

CREATE EVENT IF NOT EXISTS cleanup_expired_sessions
ON SCHEDULE EVERY 1 HOUR
DO
  CALL CleanupExpiredSessions();

-- =====================================================
-- SAMPLE DATA FOR TESTING
-- =====================================================

-- Insert organizational units
INSERT IGNORE INTO organizational_units (id, name, description) VALUES
(1, 'Engineering', 'Software development and technical teams'),
(2, 'Human Resources', 'HR and people operations'),
(3, 'Management', 'Executive and management team'),
(4, 'Quality Assurance', 'Testing and quality control');

-- Insert sample users with enhanced security
INSERT IGNORE INTO users (
    email, password, first_name, last_name, title, role, organizational_unit_id,
    is_active, account_status, must_change_password, failed_login_attempts
) VALUES
-- Admin user
('<EMAIL>', '$2b$12$K8gDKVkzjhGQqXRVQqXRVOeKQqXRVQqXRVQqXRVQqXRVQqXRVQqXRV',
 'System', 'Administrator', 'System Administrator', 'hr_admin', 2, 1, 'active', 1, 0),

-- Management users
('<EMAIL>', '$2b$12$K8gDKVkzjhGQqXRVQqXRVOeKQqXRVQqXRVQqXRVQqXRVQqXRVQqXRV',
 'John', 'Doe', 'Engineering Manager', 'manager', 1, 1, 'active', 1, 0),

-- Engineering users
('<EMAIL>', '$2b$12$K8gDKVkzjhGQqXRVQqXRVOeKQqXRVQqXRVQqXRVQqXRVQqXRVQqXRV',
 'Jane', 'Smith', 'Senior Software Engineer', 'senior_engineer', 1, 1, 'active', 1, 0),

('<EMAIL>', '$2b$12$K8gDKVkzjhGQqXRVQqXRVOeKQqXRVQqXRVQqXRVQqXRVQqXRVQqXRV',
 'Mike', 'Johnson', 'Software Engineer', 'engineer', 1, 1, 'active', 1, 0),

-- Test user for authentication testing
('<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PmvlG.',
 'Test', 'User', 'Test Engineer', 'engineer', 1, 1, 'active', 0, 0);

-- Insert sample skillsets
INSERT IGNORE INTO skillsets (name, description, category) VALUES
('JavaScript', 'JavaScript programming language', 'Programming'),
('TypeScript', 'TypeScript programming language', 'Programming'),
('React', 'React frontend framework', 'Frontend'),
('Node.js', 'Node.js backend runtime', 'Backend'),
('MySQL', 'MySQL database management', 'Database'),
('Project Management', 'Project planning and execution', 'Management'),
('Team Leadership', 'Leading and managing teams', 'Leadership');

-- Insert sample teams
INSERT IGNORE INTO teams (name, description, created_by) VALUES
('Frontend Team', 'Responsible for user interface development', 2),
('Backend Team', 'Responsible for server-side development', 2),
('DevOps Team', 'Responsible for deployment and infrastructure', 2);

-- Insert sample recognition badges (matches existing database structure)
INSERT IGNORE INTO recognition_badges (name, description, badge_type, point_value, created_by_id) VALUES
('Code Quality Champion', 'Awarded for exceptional code quality', 'achievement', 100, 1),
('Team Player', 'Awarded for outstanding collaboration', 'appreciation', 75, 1),
('Innovation Leader', 'Awarded for innovative solutions', 'skill', 150, 1),
('Milestone Master', 'Awarded for meeting project milestones', 'milestone', 50, 1);

-- =====================================================
-- FINAL NOTES
-- =====================================================
-- This script creates a complete, enhanced database schema with:
-- 1. NIS2-compliant security features
-- 2. Comprehensive user management
-- 3. Performance tracking and analytics
-- 4. Recognition and gamification systems
-- 5. Assessment and evaluation tools
-- 6. Sample data for immediate testing
--
-- Default password for all test users: "TestPassword123"
-- All users are set to must_change_password = TRUE for security
