// EHRX Application Launcher
const { exec } = require('child_process');
const http = require('http');
const express = require('express');
const path = require('path');
const os = require('os');

const app = express();
const STATUS_PORT = 3030;
const BACKEND_PORT = 4000;
const FRONTEND_PORT = 3080;

// Get server IP address for remote access
function getServerIP() {
  const interfaces = os.networkInterfaces();
  let serverIP = '127.0.0.1';
  
  // Prioritize the public IP address (eth0)
  if (interfaces.eth0) {
    const eth0IPv4 = interfaces.eth0.find(iface => iface.family === 'IPv4');
    if (eth0IPv4) {
      return eth0IPv4.address; // Return the eth0 public IP immediately
    }
  }
  
  // Fallback: Look for any non-internal IPv4 address
  Object.keys(interfaces).forEach((ifname) => {
    interfaces[ifname].forEach((iface) => {
      if (iface.family === 'IPv4' && !iface.internal) {
        serverIP = iface.address;
      }
    });
  });
  
  return serverIP;
}

const SERVER_IP = getServerIP();
console.log(`Server IP detected as: ${SERVER_IP}`);
console.log('Starting EHRX Application Launcher...');

// Function to check if a port is in use
const isPortInUse = (port) => {
  return new Promise((resolve) => {
    const server = http.createServer();
    server.once('error', () => {
      resolve(true);
    });
    server.once('listening', () => {
      server.close();
      resolve(false);
    });
    server.listen(port);
  });
};

// Function to start the backend and frontend
const startApplication = async () => {
  console.log('Starting MariaDB service...');
  
  // Check if backend server is already running
  const backendPortInUse = await isPortInUse(BACKEND_PORT);
  if (backendPortInUse) {
    console.log(`Backend server appears to be already running on port ${BACKEND_PORT}`);
  } else {
    console.log('Starting backend server with Philippine (Manila) timestamps...');
    // Set PORT environment variable for the backend and add timestamp prefix to logs
    exec(`cd /var/www/ehrx/backend && echo "[$(TZ=Asia/Manila date '+%Y-%m-%d %H:%M:%S Manila')] Starting NestJS backend server on port ${BACKEND_PORT}" > /var/www/ehrx/backend-logs.txt && PORT=${BACKEND_PORT} npm run start:dev 2>&1 | while read line; do echo "[$(TZ=Asia/Manila date '+%Y-%m-%d %H:%M:%S Manila')] $line"; done >> /var/www/ehrx/backend-logs.txt`, (error, stdout, stderr) => {
      if (error) {
        console.error(`Backend startup error: ${error.message}`);
        return;
      }
      console.log(`Backend server started successfully on port ${BACKEND_PORT}`);
    });
  }

  // Check if frontend server is already running
  const frontendPortInUse = await isPortInUse(FRONTEND_PORT);
  if (frontendPortInUse) {
    console.log(`Frontend server appears to be already running on port ${FRONTEND_PORT}`);
  } else {
    console.log('Starting frontend server with Philippine (Manila) timestamps...');
    // Frontend will read from .env file for port configuration with timestamp prefix added to logs
    exec(`cd /var/www/ehrx/frontend && echo "[$(TZ=Asia/Manila date '+%Y-%m-%d %H:%M:%S Manila')] Starting React frontend server on port ${FRONTEND_PORT}" > /var/www/ehrx/frontend-logs.txt && PATH=./node_modules/.bin:$PATH npm run start 2>&1 | while read line; do echo "[$(TZ=Asia/Manila date '+%Y-%m-%d %H:%M:%S Manila')] $line"; done >> /var/www/ehrx/frontend-logs.txt`, (error, stdout, stderr) => {
      if (error) {
        console.error(`Frontend startup error: ${error.message}`);
        return;
      }
      console.log(`Frontend server started successfully on port ${FRONTEND_PORT}`);
    });
  }
};

// Start the application
startApplication();

// Create a simple status server
app.get('/', (req, res) => {
  // Determine if remote access based on request headers
  const hostHeader = req.headers.host || '';
  const isRemoteAccess = !hostHeader.includes('localhost') && !hostHeader.includes('127.0.0.1');
  
  // Use server IP if remote access, otherwise use localhost
  const displayHost = isRemoteAccess ? SERVER_IP : 'localhost';
  
  res.send(`
    <html>
      <head>
        <title>EHRX Application Status</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
          .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
          h1 { color: #333; }
          .status { margin: 20px 0; padding: 15px; border-radius: 5px; }
          .running { background-color: #e6f7ed; color: #27ae60; }
          .pending { background-color: #f9f2e8; color: #f39c12; }
          button { background: #3498db; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; }
          .ip-info { background: #f8f9fa; padding: 10px; border-left: 4px solid #3498db; margin-bottom: 20px; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>EHRX Application Status</h1>
          <div class="ip-info">
            <p>Accessing from: <strong>${req.ip}</strong></p>
            <p>Server IP: <strong>${SERVER_IP}</strong></p>
          </div>
          <div class="status running">
            <h2>Application Starting Up</h2>
            <p>The EHRX application servers are starting. Please use the buttons below to access the application.</p>
          </div>
          <p>Backend API Server: <span id="backend-status">Starting...</span> (Port ${BACKEND_PORT})</p>
          <p>Frontend Server: <span id="frontend-status">Starting...</span> (Port ${FRONTEND_PORT})</p>
          <hr>
          <h3>Access the Application</h3>
          <p>Click the button below to open the EHRX application in a new browser window:</p>
          <button onclick="window.open('http://${displayHost}:${FRONTEND_PORT}', '_blank')">Open EHRX Application</button>
          <p>Or you can access the application directly at: <a href="http://${displayHost}:${FRONTEND_PORT}" target="_blank">http://${displayHost}:${FRONTEND_PORT}</a></p>
          <hr>
          <h3>Troubleshooting</h3>
          <p>If the application doesn't load, check the logs:</p>
          <ul>
            <li>Backend logs: <code>/var/www/ehrx/backend-logs.txt</code></li>
            <li>Frontend logs: <code>/var/www/ehrx/frontend-logs.txt</code></li>
          </ul>
        </div>
        <script>
          // Simple status checker for the servers
          function checkServerStatus(port, elementId) {
            // Use the same host for status checks as used for display
            const statusHost = '${displayHost}';
            fetch('http://' + statusHost + ':' + port + '/health')
              .then(response => {
                if (response.ok) {
                  document.getElementById(elementId).textContent = 'Running';
                  document.getElementById(elementId).style.color = '#27ae60';
                }
              })
              .catch(() => {
                setTimeout(() => checkServerStatus(port, elementId), 3000);
              });
          }
          
          setTimeout(() => {
            checkServerStatus(${BACKEND_PORT}, 'backend-status');
            checkServerStatus(${FRONTEND_PORT}, 'frontend-status');
          }, 5000);
        </script>
      </body>
    </html>
  `);
});

// Start the status server
app.listen(STATUS_PORT, '0.0.0.0', () => {
  console.log(`EHRX status server running at http://${SERVER_IP}:${STATUS_PORT}`);
  console.log(`Access your application at: http://${SERVER_IP}:${FRONTEND_PORT}`);
});
