#!/bin/bash

# EHRX Development Environment Startup Script
# Optimized for development with hot reloading and debugging features

set -e

# Source required modules
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/pid-manager.sh"
source "$SCRIPT_DIR/dependency-checker.sh"

# Development Configuration
export NODE_ENV="development"
export BACKEND_PORT="${BACKEND_PORT:-4000}"
export FRONTEND_PORT="${FRONTEND_PORT:-3080}"
export LOG_LEVEL="${LOG_LEVEL:-debug}"

# Development-specific settings
ENABLE_HOT_RELOAD=true
ENABLE_DEBUG_LOGGING=true
SKIP_BUILD=true
AUTO_INSTALL_DEPS=true

# Project paths
PROJECT_ROOT="/var/www/ehrx"
BACKEND_DIR="$PROJECT_ROOT/backend"
FRONTEND_DIR="$PROJECT_ROOT/frontend"

# Initialize logging and PID management
init_pid_management

log_message "INFO" "Starting EHRX Development Environment"
log_message "INFO" "Environment: $NODE_ENV"
log_message "INFO" "Backend Port: $BACKEND_PORT"
log_message "INFO" "Frontend Port: $FRONTEND_PORT"

# Function to check and kill existing processes on ports
check_and_kill_port() {
    local port=$1
    local service_name=$2
    
    log_message "INFO" "Checking port $port for $service_name"
    
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_message "WARN" "Port $port is in use, killing existing processes"
        
        # Get PIDs using the port
        local pids=$(lsof -ti :$port)
        
        for pid in $pids; do
            log_message "INFO" "Killing process $pid on port $port"
            kill -TERM $pid 2>/dev/null || true
            
            # Wait a moment for graceful shutdown
            sleep 2
            
            # Force kill if still running
            if kill -0 $pid 2>/dev/null; then
                log_message "WARN" "Force killing process $pid"
                kill -9 $pid 2>/dev/null || true
            fi
        done
        
        # Verify port is free
        sleep 1
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            log_message "ERROR" "Failed to free port $port"
            return 1
        fi
    fi
    
    log_message "INFO" "Port $port is available for $service_name"
    return 0
}

# Function to install dependencies if needed
install_dependencies_if_needed() {
    if [ "$AUTO_INSTALL_DEPS" = "true" ]; then
        log_message "INFO" "Checking and installing dependencies"
        
        # Check backend dependencies
        if [ ! -d "$BACKEND_DIR/node_modules" ] || [ "$BACKEND_DIR/package-lock.json" -nt "$BACKEND_DIR/node_modules" ]; then
            log_message "INFO" "Installing/updating backend dependencies"
            cd "$BACKEND_DIR"
            npm install
        fi
        
        # Check frontend dependencies
        if [ ! -d "$FRONTEND_DIR/node_modules" ] || [ "$FRONTEND_DIR/package-lock.json" -nt "$FRONTEND_DIR/node_modules" ]; then
            log_message "INFO" "Installing/updating frontend dependencies"
            cd "$FRONTEND_DIR"
            npm install
        fi
    fi
}

# Function to start backend in development mode
start_backend_dev() {
    log_message "INFO" "Starting backend in development mode"
    
    cd "$BACKEND_DIR"
    
    # Check if .env file exists
    if [ ! -f ".env" ]; then
        log_message "WARN" ".env file not found in backend directory"
        if [ -f ".env.example" ]; then
            log_message "INFO" "Copying .env.example to .env"
            cp .env.example .env
        fi
    fi
    
    # Start backend with hot reloading
    if [ "$ENABLE_HOT_RELOAD" = "true" ]; then
        log_message "INFO" "Starting backend with hot reloading (npm run start:dev)"
        npm run start:dev &
    else
        log_message "INFO" "Starting backend in development mode (npm run start)"
        npm run start &
    fi
    
    local backend_pid=$!
    save_pid "backend" "$backend_pid"
    
    log_message "INFO" "Backend started with PID: $backend_pid"
    return 0
}

# Function to start frontend in development mode
start_frontend_dev() {
    log_message "INFO" "Starting frontend in development mode"
    
    cd "$FRONTEND_DIR"
    
    # Set development environment variables
    export REACT_APP_API_URL="http://localhost:$BACKEND_PORT"
    export REACT_APP_ENV="development"
    export BROWSER=none  # Prevent automatic browser opening
    
    # Start frontend development server
    log_message "INFO" "Starting frontend development server (npm start)"
    npm start &
    
    local frontend_pid=$!
    save_pid "frontend" "$frontend_pid"
    
    log_message "INFO" "Frontend started with PID: $frontend_pid"
    return 0
}

# Function to wait for service to be ready
wait_for_service_dev() {
    local port=$1
    local service_name=$2
    local max_attempts=30
    local attempt=0
    
    log_message "INFO" "Waiting for $service_name to be ready on port $port"
    
    while [ $attempt -lt $max_attempts ]; do
        if curl -s -f "http://localhost:$port" >/dev/null 2>&1 || \
           curl -s -f "http://localhost:$port/health" >/dev/null 2>&1 || \
           curl -s -f "http://localhost:$port/api" >/dev/null 2>&1; then
            log_message "INFO" "$service_name is ready on port $port"
            return 0
        fi
        
        ((attempt++))
        log_message "DEBUG" "Waiting for $service_name... ($attempt/$max_attempts)"
        sleep 2
    done
    
    log_message "ERROR" "$service_name failed to start within expected time"
    return 1
}

# Function to show development URLs
show_development_urls() {
    echo ""
    log_message "INFO" "Development Environment Ready!"
    echo -e "${CYAN}=================================${NC}"
    echo -e "${GREEN}🚀 Frontend (React Dev Server): ${CYAN}http://localhost:$FRONTEND_PORT${NC}"
    echo -e "${GREEN}🔧 Backend (NestJS API): ${CYAN}http://localhost:$BACKEND_PORT${NC}"
    echo -e "${GREEN}📚 API Documentation: ${CYAN}http://localhost:$BACKEND_PORT/api${NC}"
    echo -e "${GREEN}🔍 Health Check: ${CYAN}http://localhost:$BACKEND_PORT/health${NC}"
    echo -e "${CYAN}=================================${NC}"
    echo ""
    log_message "INFO" "Development features enabled:"
    echo -e "${YELLOW}  • Hot reloading for both frontend and backend${NC}"
    echo -e "${YELLOW}  • Debug logging enabled${NC}"
    echo -e "${YELLOW}  • Source maps enabled${NC}"
    echo -e "${YELLOW}  • CORS configured for development${NC}"
    echo ""
    log_message "INFO" "To stop services, run: ./scripts/stop-dev.sh"
}

# Main execution
main() {
    log_message "INFO" "=== EHRX Development Startup ==="
    
    # Step 1: Run dependency checks
    log_message "INFO" "Step 1: Running dependency verification"
    if ! run_all_checks; then
        log_message "ERROR" "Dependency verification failed"
        
        if [ "$AUTO_INSTALL_DEPS" = "true" ]; then
            log_message "INFO" "Attempting to auto-fix dependencies"
            auto_fix_dependencies
            
            # Re-run checks
            if ! run_all_checks; then
                log_message "ERROR" "Auto-fix failed. Please resolve dependency issues manually."
                exit 1
            fi
        else
            log_message "ERROR" "Please resolve dependency issues before starting"
            exit 1
        fi
    fi
    
    # Step 2: Check and free ports
    log_message "INFO" "Step 2: Checking and freeing ports"
    check_and_kill_port "$BACKEND_PORT" "backend"
    check_and_kill_port "$FRONTEND_PORT" "frontend"
    
    # Step 3: Install dependencies if needed
    log_message "INFO" "Step 3: Installing dependencies if needed"
    install_dependencies_if_needed
    
    # Step 4: Start backend
    log_message "INFO" "Step 4: Starting backend service"
    if ! start_backend_dev; then
        log_message "ERROR" "Failed to start backend"
        exit 1
    fi
    
    # Step 5: Wait for backend to be ready
    log_message "INFO" "Step 5: Waiting for backend to be ready"
    if ! wait_for_service_dev "$BACKEND_PORT" "backend"; then
        log_message "ERROR" "Backend failed to start properly"
        stop_service "backend"
        exit 1
    fi
    
    # Step 6: Start frontend
    log_message "INFO" "Step 6: Starting frontend service"
    if ! start_frontend_dev; then
        log_message "ERROR" "Failed to start frontend"
        stop_service "backend"
        exit 1
    fi
    
    # Step 7: Wait for frontend to be ready
    log_message "INFO" "Step 7: Waiting for frontend to be ready"
    if ! wait_for_service_dev "$FRONTEND_PORT" "frontend"; then
        log_message "ERROR" "Frontend failed to start properly"
        stop_service "backend"
        stop_service "frontend"
        exit 1
    fi
    
    # Step 8: Show success message and URLs
    show_development_urls
    
    log_message "INFO" "Development environment started successfully"
    return 0
}

# Trap to cleanup on exit
trap 'log_message "INFO" "Received interrupt signal, cleaning up..."; cleanup_all_pid_files; exit 130' INT TERM

# Run main function
main "$@"
